/** @type {import("prettier").Config} */

module.exports = {
  // 基础配置
  printWidth: 100,         // 每行代码最大长度
  tabWidth: 2,            // 缩进空格数
  useTabs: false,         // 使用空格而不是tab缩进
  semi: true,             // 语句末尾使用分号
  singleQuote: true,      // 使用单引号
  quoteProps: 'as-needed', // 对象属性引号仅在必要时使用

  // JSX配置
  jsxSingleQuote: false,  // JSX中使用双引号
  jsxBracketSameLine: false, // JSX标签的'>'单独一行

  // 标点符号配置
  trailingComma: 'es5',   // ES5中有效的尾随逗号（数组、对象等）
  bracketSpacing: true,   // 对象字面量中的括号前后使用空格
  arrowParens: 'avoid',   // 箭头函数仅在必要时使用括号

  // 特殊语法配置
  proseWrap: 'preserve',  // markdown文本换行方式保持原样
  htmlWhitespaceSensitivity: 'strict', // HTML空格敏感度
  vueIndentScriptAndStyle: true,      // Vue文件中的script和style标签内代码是否缩进
  endOfLine: 'auto',      // 行尾换行符号自动检测

  // 嵌入式代码格式化配置
  embeddedLanguageFormatting: 'auto', // 格式化嵌入的代码块

  // 覆盖特定文件的配置
  overrides: [
    {
      files: '*.md',
      options: {
        tabWidth: 2,
        proseWrap: 'always'
      }
    },
    {
      files: '*.json',
      options: {
        parser: 'json'
      }
    },
    {
      files: '*.vue',
      options: {
        parser: 'vue'
      }
    }
  ]
};