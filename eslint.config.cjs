const globals = require('globals');
const js = require('@eslint/js');
const tseslint = require('typescript-eslint');
const eslintPluginVue = require('eslint-plugin-vue');

module.exports = [
  {
    // 全局忽略规则
    ignores: [
      '**/node_modules/**',
      'dist/**',
      'coverage/**',
      'public/**',
      '**/*.d.ts',
      '**/*.config.{js,ts,cjs}',
      'vite.config.ts'
    ]
  },
  {
    // 全局配置
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.es2021,
        ...globals.node
      },
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module'
      }
    }
  },
  // JavaScript 文件配置
  {
    files: ['**/*.{js,jsx,mjs,cjs}'],
    ...js.configs.recommended
  },
  // TypeScript 文件配置
  {
    files: ['**/*.{ts,tsx}'],
    ...tseslint.configs.recommended,
    rules: {
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-unused-vars': ['warn', {
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_'
      }],
      '@typescript-eslint/explicit-module-boundary-types': 'off'
    }
  },
  // Vue 文件配置
  {
    files: ['**/*.vue'],
    plugins: {
      vue: eslintPluginVue
    },
    processor: eslintPluginVue.processors['.vue'],
    rules: {
      ...eslintPluginVue.configs.base.rules,
      ...eslintPluginVue.configs['vue3-recommended'].rules,
      'vue/multi-word-component-names': 'off',
      'vue/require-default-prop': 'off',
      'vue/attributes-order': 'error',
      'vue/order-in-components': 'error',
      'vue/component-name-in-template-casing': ['error', 'PascalCase'],
      'vue/v-on-event-hyphenation': ['error', 'always'],
      'vue/no-unused-components': 'warn',
      'vue/no-unused-vars': 'warn'
    }
  },
  // 通用规则
  {
    rules: {
      'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off'
    }
  }
];