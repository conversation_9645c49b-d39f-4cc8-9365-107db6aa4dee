# Server Package 配置说明

## 基本信息
- `name`: `@unibabble/server` - 使用组织域名确保包名在 monorepo 中唯一
- `version`: `1.0.0` - 遵循语义化版本规范
- `description` - 简明扼要地说明包的主要功能
- `private`: `true` - 防止意外发布到公共 npm 仓库
- `type`: `module` - 声明使用 ES 模块系统，影响 Node.js 如何处理导入语句

## 脚本命令
- `dev`: 开发环境命令
  - `ts-node-dev` - 支持 TypeScript 开发环境和热重载
  - `--transpile-only` - 仅转译 TypeScript，跳过类型检查以提高开发时的重启速度
  - `--respawn` - 文件变化时自动重启服务
  - `--ignore-watch node_modules` - 忽略 node_modules 目录的文件变化
- `build`: 构建命令
  - `rimraf dist` - 清理之前的构建输出
  - `tsc` - 使用 TypeScript 编译器生成生产代码
- `start`: 生产环境启动命令，运行编译后的代码

## 依赖说明

### 运行时依赖
- `@apollo/server` - GraphQL 服务器框架
- `@unibabble/shared` - 项目内部共享代码，使用 workspace 协议
- `body-parser` - HTTP 请求体解析
- `cors` - 跨域资源共享中间件
- `dotenv` - 环境变量管理
- `express` - Web 应用框架
- `graphql` - GraphQL 查询语言实现
- `ws` - WebSocket 服务器实现

### 开发依赖
- TypeScript 类型定义
  - `@types/cors`
  - `@types/express`
  - `@types/node`
  - `@types/ws`
- 代码质量工具
  - `@typescript-eslint/eslint-plugin`
  - `@typescript-eslint/parser`
  - `eslint`
- 构建工具
  - `rimraf` - 跨平台的目录清理工具
  - `ts-node-dev` - TypeScript 开发环境运行时
  - `typescript` - TypeScript 编译器

## 其他配置
- `packageManager`: 指定使用 pnpm 作为包管理器
- `keywords`: npm 搜索关键词
- `author`: 项目团队信息
- `license`: MIT 开源许可证