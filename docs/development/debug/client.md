# 客户端远程调试指南

## 环境要求

- Windows 主机
  - VSCode
  - Chrome浏览器
  - SSH客户端
- Linux虚拟机
  - Node.js
  - 运行项目的基础环境

## 调试原理

Vue3+Vite项目的调试基于以下机制：
1. Vite开发服务器：
   - 处理Vue单文件组件的实时编译
   - 提供源码映射（sourcemap）
   - 启用热更新（HMR）
2. Chrome DevTools Protocol：
   - 实现VSCode与Chrome的通信
   - 支持断点、变量查看等调试功能
3. VSCode调试器：
   - 通过sourcemap将编译后代码映射回源码
   - 提供可视化的调试界面

## 调试环境配置（严格顺序）

### 1. SSH配置（Windows主机）

在Windows主机上配置SSH（路径：`%USERPROFILE%\.ssh\config`）：

```
Host UniBabble
  HostName ***************
  User p
  IdentityFile ~/.ssh/id_rsa
  RemoteForward 9222 127.0.0.1:9222
```

### 2. 启动Vite开发服务器（Linux虚拟机）

**重要：必须首先启动Vite服务器，这是断点调试的基础**

```bash
# 方式1：在项目根目录（推荐）
npx pnpm dev:client

# 方式2：在client目录
cd packages/client
npx pnpm dev
```

确认Vite服务器启动成功：
```bash
# 检查Vite服务是否正常运行
netstat -tulpn | grep 5173
```

### 3. Chrome配置（Windows主机）

创建专用的Chrome调试快捷方式，添加以下启动参数：
```
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="%TEMP%\chrome-debug" --no-first-run --no-default-browser-check --remote-debugging-address=0.0.0.0
```

### 4. VSCode调试配置

1. 确保Vite服务器正在运行
2. 启动Chrome调试实例
3. 验证调试端口可访问：
   ```bash
   # 在虚拟机中执行
   curl http://localhost:9222/json/list
   curl http://localhost:9222/json/version
   ```
4. 在VSCode中选择"Debug Client (Remote)"配置启动调试

## 调试环境验证

### 1. 基础连接验证

```bash
# Windows主机：验证虚拟机连接
ping ***************

# Linux虚拟机：验证服务状态
netstat -tulpn | grep 5173  # Vite服务必须正常运行
netstat -tulpn | grep 9222  # Chrome调试端口
```

### 2. 应用访问验证

1. 在Chrome中访问：`http://***************:5173`
2. 确认页面正常加载
3. 打开Chrome开发者工具，确认Sources面板中可以看到源代码

### 3. 断点调试验证

1. 在VSCode中对Vue文件设置断点
2. 确认VSCode显示断点已绑定（实心红点）
3. 触发对应的页面操作，验证是否能进入断点

## 故障排除

### 1. 断点不生效
- 首要检查项：
  - **Vite服务器是否正常运行**
  - sourcemap是否正确生成
  - VSCode调试配置是否正确

### 2. 调试连接失败
- 检查项：
  - Vite服务器状态
  - Chrome调试端口状态
  - SSH端口转发是否正常

### 3. 503访问错误
- 检查项：
  - Vite服务器运行状态
  - 虚拟机防火墙配置（5173端口）
  - 网络连接状态

## 最佳实践

1. 调试流程管理
   - 严格按照配置顺序执行
   - 确保Vite服务器始终运行
   - 定期验证服务状态

2. 开发效率
   - 使用VSCode的工作区配置保存调试设置
   - 配置快捷键加速调试操作
   - 善用Chrome开发者工具的Sources和Network面板

3. 注意事项
   - 每次重启Chrome后需要重新建立调试连接
   - 修改源码后Vite会自动重新编译
   - sourcemap的生成可能会有短暂延迟
