感谢你提供的前端框架（Vue 3）、配置文件（`config`）、路由（`router`）、入口组件（`HomeView.vue`）以及服务端没有登录端点的信息。基于这些，我将为你设计一个集成 Google Auth 的完整方案，适用于你的客户端（`useChatStore.ts`）和服务端（`initializeWebSocketIO.ts`），无需额外的登录端点，直接通过 WebSocket 认证。

---

### 前提假设

- **前端**：Vue 3 + Pinia + TypeScript，使用 Element Plus（`el-button` 等）。
- **服务端**：Node.js + Socket.IO，无独立 HTTP 登录端点。
- **目标**：通过 Google OAuth 2.0 认证用户，将 Google ID Token 传递给 WebSocket，服务端验证后绑定用户身份。

---

### 实现步骤

#### 1. 配置 Google OAuth 2.0

- **操作**：
  1. 登录 [Google Cloud Console](https://console.cloud.google.com/)。
  2. 创建项目（例如 “UniBabble”）。
  3. 在 “凭据” > “创建凭据” > “OAuth 客户端 ID”：
     - 应用类型：Web 应用。
     - 授权 JavaScript 来源：`http://localhost:5173`（开发环境，根据 `config.devServer` 调整）。
     - 授权重定向 URI：无需设置（使用隐式流）。
  4. 获取 **客户端 ID**（如 `xxxx.apps.googleusercontent.com`）。
- **保存**：将 `client_id` 添加到环境变量（`.env` 文件）：
  
  ```env
  VITE_GOOGLE_CLIENT_ID=xxxx.apps.googleusercontent.com
  ```

#### 2. 客户端集成 Google Auth

##### (1) 安装依赖

- 使用 Google Sign-In JavaScript 库：
  
  ```bash
  npm install @types/gapi @types/gapi.auth2 --save-dev
  ```
  
  （不需要额外运行时库，直接用 Google 的 CDN 脚本）

##### (2) 修改 `index.html`

- 在 `public/index.html` 中添加 Google SDK：
  
  ```html
  <!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8">
    <title>UniBabble</title>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
  </head>
  <body>
    <div id="app"></div>
  </body>
  </html>
  ```

##### (3) 更新 `useChatStore.ts`

- 添加 Google Auth 逻辑，并将令牌传递给 Socket.IO：
  
  ```typescript
  import { defineStore } from 'pinia';
  import { io, Socket } from 'socket.io-client';
  import { ref } from 'vue';
  import { config } from '@/config';
  import { catchErr, logger, WebSocketEvents, genUserIdBySocket, createCliErr } from '@unibabble/shared';
  
  const MODULE_NAME = 'client:useChatStore';
  
  declare global {
    interface Window {
      google: any;
    }
  }
  
  const useChatStore = defineStore('chat', () => {
    const isConnected = ref(false);
    const isConnecting = ref(false);
    const currentUser = ref<User | null>(null);
    const currentRoom = ref<Room | null>(null);
    const messages = ref<Map<string, Message>>(new Map());
    const error = ref<ErrorPayload | null>(null);
    const googleToken = ref<string | null>(null);
    const originalClientSocket = ref<ClientSocket | undefined>(undefined);
  
    // Google Auth 初始化
    async function initGoogleAuth(): Promise<void> {
      return new Promise((resolve) => {
        window.google.accounts.id.initialize({
          client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
          callback: (response: any) => {
            googleToken.value = response.credential;
            const payload = JSON.parse(atob(response.credential.split('.')[1]));
            currentUser.value = {
              id: payload.sub, // Google 用户 ID
              name: payload.name || 'User',
              lang: { target: 'EN-US', source: 'EN', name: 'English' },
            };
            resolve();
          },
        });
      });
    }
  
    // 触发 Google 登录弹窗
    async function loginWithGoogle() {
      if (!googleToken.value) {
        await initGoogleAuth();
        window.google.accounts.id.prompt(); // 显示 Google 登录弹窗
      }
    }
  
    // 修改 connect 方法
    async function connect(username?: string, url?: string) {
      if (!googleToken.value) {
        await loginWithGoogle();
        if (!googleToken.value) throw new Error('Google authentication failed');
      }
  
      isConnecting.value = true;
      const wsUrl = url ?? config.api.wsUrl.replace('ws://', 'wss://');
      originalClientSocket.value = io(wsUrl, {
        auth: { token: googleToken.value }, // 传递 Google ID Token
        path: config.paths.ws,
        secure: true,
        reconnection: true,
        reconnectionAttempts: config.ws.maxReconnectAttempts,
        reconnectionDelay: config.ws.reconnectBaseDelay,
        reconnectionDelayMax: config.ws.maxReconnectDelay,
        timeout: config.ws.connectTimeout,
        transports: ['websocket'],
      }) as ClientSocket;
  
      originalClientSocket.value.connect();
  
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(createCliErr(WebErrorCode.CONNECT_TIMEOUT, 'Connect Timeout', 'connect', MODULE_NAME));
        }, config.ws.connectTimeout);
  
        originalClientSocket.value!.once(WebSocketEvents.STANDARD.CLIENT.CONNECT, () => {
          clearTimeout(timeout);
          isConnecting.value = false;
          isConnected.value = true;
          logger.info('Connected with Google Auth', { module: MODULE_NAME, userId: currentUser.value?.id });
          resolve();
        });
  
        originalClientSocket.value!.once(WebSocketEvents.STANDARD.CLIENT.CONNECT_ERROR, (err) => {
          clearTimeout(timeout);
          reject(err);
        });
      });
    }
  
    // 其余方法保持不变，略...
  
    return {
      isConnected,
      isConnecting,
      currentUser,
      currentRoom,
      messages,
      error,
      googleToken,
      connect,
      loginWithGoogle,
      // 其他原有方法...
    };
  });
  
  export { useChatStore };
  ```

##### (4) 更新 `HomeView.vue`

- 添加 Google 登录按钮并调整连接逻辑：
  
  ```vue
  <template>
    <div class="chat-view">
      <header class="header">
        <div class="header-left">
          <h1>{{ config.app.title }}</h1>
          <button v-if="!currentUser" class="google-login" @click="loginWithGoogle">
            Login with Google
          </button>
          <button v-else class="username-button" @click="showUsernameDialog = true">
            {{ username }}
            <span class="edit-icon">✎</span>
          </button>
        </div>
        <div class="header-right">
          <div :class="['connection-status', { 'connection-status--connected': isConnected }]">
            {{ connectionStatus }}
          </div>
        </div>
      </header>
  
      <div v-if="!currentUser" class="chat-view__setup">
        <p>Please login with Google to start chatting.</p>
      </div>
  
      <div v-else-if="(!isConnected || isChatRoomMounted) && (!currentRoom)" class="chat-view__setup">
        <h2>Your Language</h2>
        <LanguageSelector />
        <el-button type="primary" @click="handleConnect2Room()" :loading="isConnecting">
          {{ startBtnName }}
        </el-button>
      </div>
  
      <!-- 其余模板保持不变 -->
    </div>
  </template>
  
  <script setup lang="ts">
  import { useChatStore } from '@/stores';
  import { config } from '@/config';
  import { ref, computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  
  const chatStore = useChatStore();
  const route = useRoute();
  const router = useRouter();
  
  const roomId = ref<string>(route.params.roomId as string || '');
  const urlRoomId = computed({
    get: () => roomId.value,
    set: (value: string) => {
      roomId.value = value;
      if (value === '') router.replace({ params: { roomId: undefined } });
      else route.params.roomId = value;
    },
  });
  
  const currentUser = computed(() => chatStore.currentUser);
  const isConnected = computed(() => chatStore.isConnected);
  const isConnecting = computed(() => chatStore.isConnecting);
  const connectionStatus = computed(() => (isConnected.value ? 'Connected' : isConnecting.value ? 'Connecting...' : 'Not connected'));
  
  const username = computed(() => currentUser.value?.name || 'Guest');
  const showUsernameDialog = ref(false);
  
  const loginWithGoogle = async () => {
    await chatStore.loginWithGoogle();
  };
  
  const handleConnect2Room = async () => {
    if (!chatStore.currentUser) {
      await chatStore.loginWithGoogle();
    }
    await chatStore.connect();
    await chatStore.joinRoom(urlRoomId.value);
  };
  
  // 其他逻辑保持不变，略...
  </script>
  
  <style scoped>
  .google-login {
    padding: 0.5rem 1rem;
    background-color: #4285f4;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  .google-login:hover {
    background-color: #357abd;
  }
  </style>
  ```

#### 3. 服务端验证 Google Token

- **安装依赖**：
  
  ```bash
  npm install google-auth-library
  ```

- **修改 `initializeWebSocketIO.ts`**：
  
  ```typescript
  import { Server } from 'socket.io';
  import { OAuth2Client } from 'google-auth-library';
  import { config } from '@/configs/index.js';
  
  const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID); // 从环境变量读取
  
  const initializeWebSocketIO = (httpServer: any) => {
    const io = new Server(httpServer, {
      path: config.paths.ws,
      cors: { origin: config.cors.origin },
    });
  
    io.use(async (socket, next) => {
      const token = socket.handshake.auth.token;
      if (!token) return next(new Error('No token provided'));
  
      try {
        const ticket = await client.verifyIdToken({
          idToken: token,
          audience: process.env.GOOGLE_CLIENT_ID,
        });
        const payload = ticket.getPayload();
        socket.data.userId = payload?.sub; // Google 用户 ID
        socket.data.name = payload?.name;
        next();
      } catch (err) {
        next(new Error('Invalid Google token'));
      }
    });
  
    io.on(WebSocketEvents.STANDARD.SERVER.CONNECTION, (socket: ServerSocket) => {
      socket.on(WebSocketEvents.ROOM.JOIN, (data) => {
        if (!socket.data.userId) {
          emitErr(socket, WebErrorCode.AUTH_FAILED, 'Unauthorized');
          return;
        }
        data.user = { id: socket.data.userId, name: socket.data.name, lang: data.user.lang };
        roomManager.handleRoomJoin(io, socket, data);
      });
      // 其他事件保持不变...
    });
  
    return io;
  };
  
  export { initializeWebSocketIO, serverState, roomManager, messageManager, userManager, handleDisconnect };
  ```

- **服务端环境变量**：
  在服务端根目录添加 `.env`：
  
  ```env
  GOOGLE_CLIENT_ID=xxxx.apps.googleusercontent.com
  ```

#### 4. 配置环境变量

- **客户端 `.env`**：
  
  ```env
  VITE_GOOGLE_CLIENT_ID=xxxx.apps.googleusercontent.com
  VITE_API_URL=http://localhost:3000
  VITE_WS_URL=ws://localhost:3000
  VITE_WS_PATH=/socket.io
  ```
- **服务端 `.env`**：
  
  ```env
  GOOGLE_CLIENT_ID=xxxx.apps.googleusercontent.com
  PORT=3000
  ```

---

### 运行与测试

1. **启动服务端**：
   - 确保服务端使用 HTTPS（或开发时暂用 HTTP，生产环境必须启用 HTTPS）。
   - `node server.js`（假设主文件是 `server.js`）。
2. **启动客户端**：
   - `npm run dev`，访问 `http://localhost:5173`。
3. **测试登录**：
   - 点击 “Login with Google”，选择 Google 账户。
   - 认证成功后，`currentUser` 将包含 Google 用户信息，WebSocket 连接自动建立。

---

### 潜在问题与改进

1. **令牌过期**：
   - Google ID Token 有效期为 1 小时，未处理刷新。
   - **改进**：监听 `connect_error`，如果因令牌过期失败，重新触发 `loginWithGoogle`。
     
     ```typescript
     originalClientSocket.value!.on('connect_error', async (err) => {
       if (err.message === 'Invalid Google token') {
         await loginWithGoogle();
         await connect();
       }
     });
     ```
2. **安全性**：
   - 当前仅验证 ID Token，未实现端到端加密。
   - **改进**：参考前文建议，添加消息加密。

---

### 下一步

- **提供反馈**：
  - 如果有其他组件（如 `ChatRoom.vue`）或服务端主文件（`server.js`），贴出来让我进一步整合。
- **确认配置**：
  - 请获取 Google `client_id` 并测试上述代码，告诉我运行结果或错误。
- **额外需求**：
  - 如果需要调整 UI 或添加登出功能，告诉我具体要求。

现在，你可以先按照上述步骤配置并运行，有什么问题或需要细化的地方随时告诉我！
