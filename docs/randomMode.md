# 随机匹配系统设计文档

## 1. 概述

本文档描述了 UniBabble 中的随机匹配聊天功能的设计和实现。该功能允许用户进入随机匹配模式，系统会自动将用户与其他处于随机模式的用户进行匹配，以便开始聊天。

## 2. 核心概念

- **随机模式**：
    用户可以选择不同的聊天模式，Random打开后, 当前用户的id进入随机池
    点击start会随机选择用户并发送聊天邀请. 默认10次, 每次等待20s
    接受邀请后, 双方从随机池中移除
    离开房间后(leave/kick), 移入随机池

- **匹配**：将两个随机模式用户配对的过程
- **聊天邀请**：匹配成功后，系统会向双方发送聊天邀请

## 3. 系统架构

### 3.1 组件

- **WebSocket 服务器**：处理实时通信
- **匹配服务**：管理用户匹配逻辑
- **状态存储**：维护用户状态和匹配状态

### 3.2 数据流

1. 用户通过 WebSocket 连接服务器
2. 用户发送 `JOIN_RANDOM` 消息进入随机匹配
3. 匹配服务尝试将用户与其他用户匹配
4. 匹配成功后，创建聊天室并通知双方
5. 用户接受邀请后进入聊天室


## 4. 数据结构

### 4.1 服务器状态 (ServerState)

```typescript
interface ServerState {
  // 现有字段...
  
  // 随机匹配相关
  randomUsers: Set<string>;           // 处于随机模式的用户ID集合
  userSockets: Map<string, WebSocket>; // 用户ID到WebSocket的映射
  userMatchStatus: Map<string, {     // 用户匹配状态
    status: 'idle' | 'matching' | 'matched';
    matchedUserId?: string;          // 匹配到的用户ID
    timeoutId?: NodeJS.Timeout;       // 匹配超时定时器
  }>;
}
```

### 4.2 WebSocket 消息类型

```typescript
type WsMessage = 
  | { type: 'JOIN_RANDOM' }                     // 加入随机匹配
  | { type: 'LEAVE_RANDOM' }                    // 离开随机匹配
  | { type: 'MATCH_FOUND', userId: string }      // 找到匹配
  | { type: 'MATCH_TIMEOUT' }                   // 匹配超时
  | { type: 'CHAT_INVITE', fromUserId: string, roomId: string } // 聊天邀请
  | { type: 'CHAT_ACCEPT', roomId: string }      // 接受邀请
  | { type: 'CHAT_REJECT' }                     // 拒绝邀请
  | { type: 'MATCH_CANCELED', reason: string };  // 匹配取消
```

## 5. 核心算法

### 5.1 加入随机匹配

1. 验证用户身份
2. 将用户添加到 `randomUsers` 集合
3. 设置用户状态为 `matching`
4. 设置匹配超时（默认30秒）
5. 尝试匹配其他用户

```typescript
function handleJoinRandom(userId: string, ws: WebSocket, state: ServerState) {
  if (state.randomUsers.has(userId)) {
    return sendError(ws, 'Already in random matching');
  }
  
  state.randomUsers.add(userId);
  const timeoutId = setTimeout(() => handleMatchTimeout(userId, state), 30000);
  
  state.userMatchStatus.set(userId, {
    status: 'matching',
    timeoutId
  });
  
  tryMatchUsers(state);
}
```

### 5.2 用户匹配

1. 从 `randomUsers` 中获取所有待匹配用户
2. 随机选择两个用户进行匹配
3. 更新双方状态为 `matched`
4. 通知双方匹配成功

```typescript
function tryMatchUsers(state: ServerState) {
  const availableUsers = Array.from(state.randomUsers)
    .filter(userId => {
      const status = state.userMatchStatus.get(userId);
      return status?.status === 'matching';
    });
  
  while (availableUsers.length >= 2) {
    const user1 = availableUsers.splice(
      Math.floor(Math.random() * availableUsers.length), 1)[0];
    const user2 = availableUsers.splice(
      Math.floor(Math.random() * availableUsers.length), 1)[0];
    
    createMatch(user1, user2, state);
  }
}
```

### 5.3 处理匹配超时

```typescript
function handleMatchTimeout(userId: string, state: ServerState) {
  if (!state.randomUsers.has(userId)) return;
  
  const ws = state.userSockets.get(userId);
  if (ws?.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify({ type: 'MATCH_TIMEOUT' }));
  }
  
  cleanupUser(userId, state);
}
```

## 6. 错误处理

### 6.1 连接断开

当用户断开连接时：
1. 从 `randomUsers` 中移除用户
2. 清理匹配状态
3. 如果用户正在匹配中，通知匹配的对方

### 6.2 消息处理错误

- 无效消息：记录错误并忽略
- 未授权访问：关闭连接
- 处理超时：清理资源并通知用户

## 7. 性能考虑

### 7.1 内存使用

- 使用 `Set` 存储随机用户ID，确保唯一性
- 使用 `Map` 存储用户状态，快速查找
- 定期清理超时用户

### 7.2 扩展性

- 支持水平扩展多个 WebSocket 服务器
- 使用 Redis 存储共享状态
- 实现分片处理大量用户

## 8. 安全考虑

1. **认证**：验证用户身份
2. **授权**：检查用户权限
3. **速率限制**：防止滥用
4. **输入验证**：验证所有输入数据
5. **HTTPS**：使用安全连接

## 9. 监控和日志

- 记录关键操作（加入、离开、匹配等）
- 监控系统性能
- 设置告警

## 10. 未来优化

1. **智能匹配**：基于用户兴趣、语言等优化匹配
2. **评分系统**：实现用户评分机制
3. **多语言支持**：优先匹配相同语言用户
4. **地理位置**：考虑用户地理位置进行匹配

## 11. API 参考

### 11.1 加入随机匹配

**请求**
```typescript
{
  "type": "JOIN_RANDOM"
}
```

**响应**
```typescript
{
  "type": "MATCH_STARTED"
}
```

### 11.2 匹配成功通知

```typescript
{
  "type": "MATCH_FOUND",
  "userId": "user123",
  "userInfo": {
    // 用户信息
  }
}
```

### 11.3 聊天邀请

```typescript
{
  "type": "CHAT_INVITE",
  "fromUserId": "user123",
  "roomId": "room456"
}
```

## 12. 实现说明

### 12.1 文件结构

```
src/
  server/
    ws.ts              # WebSocket 服务器
    matchmaking.ts     # 匹配逻辑
    types/
      index.ts        # 类型定义
```

### 12.2 依赖项

- `ws`: WebSocket 服务器实现
- `uuid`: 生成唯一ID
- `ioredis`: Redis 客户端（用于分布式部署）

## 13. 部署指南

1. 安装依赖：`npm install`
2. 配置环境变量：
   ```env
   PORT=3000
   REDIS_URL=redis://localhost:6379
   ```
3. 启动服务器：`npm run start`

## 14. 测试

1. 单元测试：`npm test`
2. 集成测试：`npm run test:integration`
3. 负载测试：使用 `artillery` 进行压力测试

## 15. 故障排除

### 15.1 常见问题

1. **连接问题**：检查 WebSocket URL 和端口
2. **匹配失败**：确认有足够多的在线用户
3. **性能问题**：检查服务器资源使用情况

### 15.2 日志分析

- 错误日志：`logs/error.log`
- 访问日志：`logs/access.log`
- 性能日志：`logs/performance.log`

## 16. 贡献指南

1. Fork 仓库
2. 创建特性分支：`git checkout -b feature/random-matching`
3. 提交更改：`git commit -am 'Add random matching feature'`
4. 推送到分支：`git push origin feature/random-matching`
5. 创建 Pull Request

## 许可证

MIT