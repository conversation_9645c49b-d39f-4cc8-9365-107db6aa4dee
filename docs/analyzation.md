# UniBabble 项目深入分析

## 1. 项目架构

### 客户端架构
```
+-------------------+       +-------------------+       +-------------------+
|     UI Layer      |       |  State Management |       |  API Layer        |
| (Vue Components)  |<----->| (Pinia Stores)    |<----->| (REST + WebSocket)|
+-------------------+       +-------------------+       +-------------------+
```

#### UI Layer
- **ChatRoom.vue**: 主聊天室界面，包含消息列表和输入框
- **MessageList.vue**: 消息列表组件，负责消息渲染
- **MessageInput.vue**: 消息输入组件，处理用户输入
- **其他辅助组件**: 如用户列表、设置面板等

#### State Management
- **chatStore**: 管理聊天室相关状态（用户、消息、房间信息）
- **userStore**: 管理用户认证和偏好设置
- **configStore**: 管理客户端配置

#### API Layer
- **REST API**: 处理非实时数据请求（如房间信息、用户信息）
- **WebSocket**: 处理实时消息传递和状态更新
- **错误处理**: 统一处理 API 错误和重试逻辑

### 服务端架构
```
+-------------------+       +-------------------+       +-------------------+
|  WebSocket Layer  |       |  Business Logic   |       |  Data Layer       |
| (Socket.IO)       |<----->| (Room/Msg Mgmt)   |<----->| (In-Memory State) |
+-------------------+       +-------------------+       +-------------------+
```

#### WebSocket Layer
- **连接管理**: 处理客户端连接、断开和重连
- **消息路由**: 将消息分发到对应的业务逻辑处理
- **心跳机制**: 保持连接活跃，检测断开

#### Business Logic
- **房间管理**: 创建、加入、离开房间
- **消息处理**: 消息验证、翻译、广播
- **用户管理**: 用户状态跟踪、权限控制
- **定时任务**: 房间状态检查、资源清理

#### Data Layer
- **内存存储**: 使用 Map 和 Set 存储房间、用户和消息状态
- **状态同步**: 通过 WebSocket 保持客户端和服务端状态一致
- **资源清理**: 定时清理不活跃的房间和用户

### 共享模块架构
```
+-------------------+       +-------------------+       +-------------------+
|  Type Definitions |       |  Error Handling   |       |  Utility Classes  |
| (Base Types)      |<----->| (Error Codes)     |<----->| (Concurrent Tools)|
+-------------------+       +-------------------+       +-------------------+
```

#### Type Definitions
- **核心类型**: 用户、房间、消息等
- **配置类型**: 客户端和服务端共享的配置定义
- **API 类型**: REST 和 WebSocket 接口的请求/响应类型

#### Error Handling
- **错误码体系**: 统一的错误码定义
- **错误处理工具**: 创建和传递错误信息的工具函数
- **错误日志**: 记录错误上下文信息

#### Utility Classes
- **并发控制**: 如 ConcurrentMap 处理并发访问
- **数据结构**: 如 FixedLengthArray 实现固定长度数组
- **ID 生成**: 生成唯一的用户 ID 和房间 ID

## 2. 核心业务流程

### 用户登录
1. 客户端通过 Google OAuth 进行认证
2. 服务器验证 token 并建立用户会话
3. 返回用户信息和认证状态
4. 客户端更新用户状态和偏好设置

### 加入聊天室
1. 客户端发送加入房间请求
2. 服务器检查房间状态和用户权限
3. 创建或加入房间，更新房间用户列表
4. 通过 WebSocket 广播新用户加入消息
5. 客户端更新房间状态和用户列表

### 发送消息
1. 客户端输入消息并发送
2. 服务器接收消息，进行内容验证和速率限制
3. 调用翻译服务（如 DeepL）进行消息翻译
4. 广播消息给房间内所有用户
5. 更新消息状态（发送中、已送达、已读）
6. 客户端接收消息并更新消息列表

### 接收消息
1. 服务器通过 WebSocket 推送消息
2. 客户端接收消息并更新消息列表
3. 根据用户语言偏好显示原始或翻译后的内容
4. 更新消息状态（已送达、已读）
5. 客户端进行消息渲染和显示

### 离开聊天室
1. 用户主动离开或超时断开
2. 服务器更新房间用户列表
3. 广播用户离开消息
4. 清理相关资源（如消息队列、状态等）
5. 客户端更新房间状态和用户列表

## 3. 运行环境

### 客户端
- **运行环境**: 现代浏览器
- **技术栈**: 
  - Vue.js 3 (Composition API)
  - Vite 构建工具
  - Element Plus UI 组件库
  - Pinia 状态管理
  - TypeScript

### 服务端
- **运行环境**: Node.js
- **技术栈**:
  - Express.js
  - Socket.IO
  - TypeScript
  - DeepL 翻译 API
  - 其他第三方服务集成

### 共享模块
- **运行环境**: 同时支持浏览器和 Node.js
- **技术栈**:
  - TypeScript 类型定义
  - 通用工具类和错误处理
  - 配置管理

## 4. 关键设计

### 实时通信
- 使用 WebSocket 实现实时消息传递
- 支持消息状态跟踪（发送中、已送达、已读）
- 实现消息重试机制

### 多语言支持
- 集成 DeepL 翻译服务
- 支持自动检测和手动选择目标语言
- 消息内容缓存和复用

### 房间管理
- 支持房间创建、加入、离开
- 实现房间状态监控（活跃、空闲、关闭）
- 支持管理员权限管理

### 错误处理
- 统一的错误码体系
- 详细的错误信息记录
- 客户端友好的错误提示

### 性能优化
- 消息速率限制
- 房间状态定时检查
- 用户状态监控
- 资源清理机制

## 5. 扩展性考虑

- 模块化设计，易于添加新功能
- 支持多种翻译服务（DeepL、Mistral）
- 可配置的房间和消息限制
- 详细的日志和监控支持
- 支持多种认证方式（目前支持 Google OAuth）

