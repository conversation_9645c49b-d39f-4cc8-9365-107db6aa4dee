# UniBabble MVP 开发计划

## 项目概述
UniBabble MVP 版本专注于实现核心的双人实时翻译聊天功能，确保基础功能的可用性和稳定性。

## 实现步骤

### 一、基础设施搭建 ✅

1. **项目初始化** ✅
   - [x] 创建基础项目结构
   - [x] 配置开发环境
   - [x] 添加必要的依赖
   - [x] 设置开发规范

2. **路由设计** ✅
   - [x] 配置 Vue Router
   - [x] 设计路由结构
     - [x] 首页（语言选择）
     - [x] 聊天页面
     - [x] 404页面

3. **状态管理** ✅
   - [x] 配置 Pinia store
   - [x] 设计状态结构
     - [x] 用户状态（语言偏好）
     - [x] 聊天状态
     - [x] 连接状态

### 二、后端服务实现 ✅

1. **基础架构** ✅
   - [x] Express 服务器搭建
   - [x] WebSocket 服务配置
   - [x] 项目结构规划
   - [x] 开发环境配置

2. **核心功能** ✅
   - [x] WebSocket 连接管理
   - [x] 房间管理
     - [x] 创建房间
     - [x] 加入房间
     - [x] 离开房间
     - [x] 自动清理机制
   - [x] 消息处理
     - [x] 消息转发
     - [x] 消息确认机制
     - [ ] 翻译集成

3. **API 设计** ✅
   - [x] WebSocket 事件定义
     - [x] 基础事件（连接、加入、离开）
     - [x] 消息事件（发送、接收）
     - [x] 确认事件（送达、已读）
   - [x] HTTP 接口定义
   - [x] 错误处理机制

   **WebSocket 事件关系详解**:
   ```typescript
   // WebSocket 事件映射关系

   1. 连接流程
   客户端事件:
   - CONNECT_CLIENT
     - 触发: chatStore.connect() 调用
     - 数据: { userId: string, userName: string, language: string }
     - 影响: 初始化 WebSocket 连接

   服务端事件:
   - CONNECT_SERVER
     - 监听: io.on(CONNECT_SERVER)
     - 处理: 创建 socket 连接，初始化用户信息
     - 影响: 用户连接状态更新，准备接收后续事件

   2. 房间管理流程
   客户端事件:
   - ROOM_JOIN
     - 触发: chatStore.joinRoom() 调用
     - 数据: JoinRoomPayload
     - 影响: 等待加入房间确认
   - ROOM_LEAVE
     - 触发: 用户主动离开或组件销毁
     - 数据: LeaveRoomPayload
     - 影响: 等待离开房间确认

   服务端事件:
   - ROOM_JOIN (监听)
     - 处理: roomHandlers.handleRoomJoin
     - 影响:
       1. 更新房间用户列表
       2. 广播房间状态
       3. 发送历史消息
   - ROOM_LEAVE (监听)
     - 处理: roomHandlers.handleRoomLeave
     - 影响:
       1. 更新房间用户列表
       2. 广播房间状态
   - ROOM_UPDATE (广播)
     - 触发: 房间状态变更
     - 数据: { room: Room }
     - 影响: 所有房间用户更新房间状态

   3. 消息处理流程
   客户端事件:
   - MESSAGE_SEND
     - 触发: 用户发送消息
     - 数据: SendMessagePayload
     - 影响: 等待消息确认

   服务端事件:
   - MESSAGE_SEND (监听)
     - 处理: messageHandlers.handleMessageSend
     - 影响:
       1. 存储消息
       2. 发送确认
       3. 广播消息
   - MESSAGE_RECEIVE (广播)
     - 触发: 新消息到达
     - 数据: MessageReceivePayload
     - 影响: 所有房间用户接收新消息
   - MESSAGE_CONFIRM (响应)
     - 触发: 消息成功处理
     - 数据: { messageId: string }
     - 影响: 发送者更新消息状态
   - MESSAGE_HISTORY (响应)
     - 触发: 用户加入房间
     - 数据: { messages: Message[] }
     - 影响: 用户加载历史消息

   4. 错误处理流程
   服务端事件:
   - ERROR (广播)
     - 触发: 发生错误时
     - 数据: ErrorPayload
     - 影响: 客户端显示错误信息

   // 关键事件数据结构
   interface JoinRoomPayload {
     roomId: string;
     userId: string;
     language: string;
     userName?: string;
   }

   interface LeaveRoomPayload {
     roomId: string;
     userId: string;
     timestamp: number;
   }

   interface SendMessagePayload {
     content: string;
     roomId: string;
   }

   interface MessageReceivePayload {
     message: Message;
     translations: Record<string, string>;
   }

   interface ErrorPayload {
     code: WebSocketErrorCode;
     message: string;
     roomId?: string;
     userId?: string;
   }
   ```

4. **数据结构** ✅
   ```typescript
   // 房间定义
   interface Room {
     id: string;              // 房间唯一标识
     users: string[];         // 房间内的用户ID列表
     createdAt: number;       // 房间创建时间
     lastActiveAt: number;    // 最后活动时间
     status: 'active' | 'inactive'; // 房间状态
   }

   // 消息定义
   interface Message {
     id: string;
     type: 'text';
     sender: User;
     originalContent: string;
     originalLanguage: string;
     translations: Record<string, string>;
     timestamp: number;
   }

   // 用户定义
   interface User {
     id: string;
     name: string;
     language: string;
   }
   ```

5. **配置系统** ✅
   ```typescript
   interface ServerConfig {
     port: number;
     host: string;
     env: 'development' | 'production';
     paths: {
       ws: string;
       graphql: string;
     };
     cors: {
       origin: string | string[];
       methods: string[];
       allowedHeaders: string[];
     };
     ws: {
       pingInterval: number;
       pingTimeout: number;
     };
     log: {
       level: 'debug' | 'info' | 'warn' | 'error';
     };
   }
   ```

### 三、前端核心功能实现 🔄

1. **WebSocket 客户端** 📝
   - [ ] WebSocket 服务类
     ```typescript
     class WebSocketService {
       connect(roomId: string): void
       sendMessage(content: string, fromLang: string, toLang: string): void
       disconnect(): void
     }
     ```
   - [ ] 消息处理
   - [ ] 基础错误处理

2. **状态管理更新** 📝
   - [ ] WebSocket 状态
   - [ ] 消息记录
   - [ ] 房间信息

3. **UI 组件** 🔄
   - [x] 基础消息列表
   - [x] 消息输入框
   - [ ] 连接状态显示
   - [ ] 翻译状态显示

### 四、功能整合 📝

1. **消息流程**
   - [ ] 发送消息流程
   - [ ] 接收消息流程
   - [ ] 翻译流程
   - [ ] 错误处理流程

2. **会话管理**
   - [ ] 会话创建
   - [ ] 会话链接生成
   - [ ] 会话加入机制

3. **用户体验**
   - [ ] 加载状态
   - [ ] 错误提示
   - [ ] 操作反馈

### 五、测试验证 📝

1. **单元测试**
   - [ ] 工具函数测试
   - [ ] 组件测试
   - [ ] Store 测试

2. **集成测试**
   - [ ] 消息发送/接收测试
   - [ ] 翻译功能测试
   - [ ] WebSocket 连接测试

3. **用户测试**
   - [ ] 不同语言场景测试
   - [ ] 网络异常场景测试
   - [ ] 浏览器兼容性测试

### 六、部署准备 📝

1. **性能优化**
   - [ ] 代码分割
   - [ ] 资源优化
   - [ ] 首屏加载优化

2. **部署配置**
   - [ ] 环境变量配置
   - [ ] 构建脚本优化
   - [ ] 错误监控

3. **文档完善** 🔄
   - [x] API 文档结构
   - [x] 开发规范文档
   - [ ] 部署文档
   - [ ] 使用文档

## 前端界面分析与优化建议

### 前端界面特点总结

1.  **技术栈与基础库:**
    *   采用 Vue 3 作为核心框架，配合 TypeScript 进行类型约束。
    *   深度整合 Element Plus UI 组件库，大部分基础组件（输入框、按钮、选择器、对话框、标签、加载指示器等）均来自 Element Plus。
    *   使用 Pinia 进行状态管理，UI 的动态变化由 `chatStore` 等状态驱动。
    *   使用 SCSS 进行样式编写，并通过 `@/styles/_variables.scss` 文件定义了设计规范变量。

2.  **整体结构与布局:**
    *   **单页面应用 (SPA):** 通过 Vue Router 实现视图切换。
    *   **组件化:** UI 被拆分为多个组件，如 `HomeView` (主视图)、`ChatRoom` (聊天室核心)、`MessageList` (消息列表)、`MessageInput` (消息输入)、`LanguageSelector` (语言选择器)。
    *   **响应式布局基础:** 主要依赖 Flexbox 进行布局。`App.vue` 和 `HomeView.vue` 尝试实现全高布局。`MessageInput` 固定在聊天界面的底部。
    *   **条件渲染:** 大量使用 `v-if` 根据应用状态（用户登录、连接状态、房间信息、错误等）动态显示不同UI部分。

3.  **样式与视觉风格:**
    *   **Element Plus 默认风格:** 界面整体呈现 Element Plus 提供的简洁、现代的风格。
    *   **设计规范 (`_variables.scss`):** 定义了包括颜色、字号、间距、圆角、断点、z-index 在内的 SCSS 变量，为统一视觉风格提供了基础。例如 `$color-primary: #1890ff;` (蓝色主调)。
    *   **全局基础样式 (`base.css`):** 包含了源自 Vue 官方主题的 CSS 自定义属性（如 `--vt-c-white`, `--vt-c-black`），支持暗黑模式切换，并提供了 CSS 重置和基础排版样式。
    *   **组件作用域样式:** 组件多使用 `<style scoped>` 来隔离样式。
    *   **少量自定义样式:** 在 Element Plus 基础上，各组件根据需要添加了自定义的布局和细节样式。

4.  **核心功能界面呈现:**
    *   **聊天前准备:**
        *   `HomeView.vue`: 根据用户是否登录、是否已选择语言或加入房间，显示不同引导界面（如登录按钮、语言选择器、开始/加入房间按钮）。
        *   `LanguageSelector.vue`: 提供标准下拉列表供用户选择语言。
    *   **聊天室 (`ChatRoom.vue`):**
        *   **房间信息区:** 显示房间ID（可复制链接）、用户列表（用 `el-tag` 展示，区分当前用户）、当前用户语言、离开/重入房间按钮、下载聊天记录按钮、翻译开关、显示原文开关、管理员踢人入口。
        *   **消息列表 (`MessageList.vue`):**
            *   区分自己与他人的消息气泡样式（背景色、左右对齐）。
            *   支持系统消息和文本消息类型。
            *   根据翻译开关和显示原文开关，条件显示原文和译文。
            *   有新消息且用户未滚动到底部时，有 "New Message" 提示条。
            *   为发送失败的消息提供重发按钮。
        *   **消息输入 (`MessageInput.vue`):**
            *   使用 `el-input` 配合 `el-button` (发送按钮)。
            *   支持回车发送，发送中按钮有加载状态并禁用输入。
            *   输入框的可用性和占位符根据连接状态改变。
    *   **反馈机制:**
        *   连接状态（已连接、连接中、未连接）有明确文本和视觉提示。
        *   异步操作有加载指示（如 `el-loading`）。
        *   错误通过 `ElNotification` (全局通知) 或 `el-dialog` (对话框) 显示。
        *   操作确认使用 `ElMessageBox.confirm`。

### 当前界面的主要优点

*   **坚实的技术选型:** Vue 3、TypeScript、Pinia 和 Element Plus 为项目提供了一个现代化且高效的开发基础。
*   **良好的组件化实践:** UI 结构清晰，核心功能被拆分成可复用和维护的组件。
*   **状态驱动的动态UI:** 界面能够根据应用状态实时响应，用户体验流畅。
*   **统一的样式基础:** 通过 `_variables.scss` 和 `base.css`，为视觉一致性和主题化打下了良好基础。特别是 `base.css` 中对暗黑模式的预先支持。
*   **核心功能覆盖:** MVP 所需的核心聊天功能均有对应的UI元素实现。

### UI/UX优化建议

尽管基础功能已经具备雏形，但为了提升用户体验和视觉效果，可以从以下方面进行优化：

1.  **视觉打磨与品牌化:**
    *   **色彩系统整合:**
        *   梳理 `base.css` 中的 CSS 自定义属性（偏 Vue 默认主题色系）与 `_variables.scss` 中的 SCSS 变量（主色 `#1890ff` 偏 Ant Design 风格）之间的关系。确定一套主导的色彩规范，并确保两者协同工作或统一。
        *   考虑让 SCSS 变量也支持暗黑模式，或者让 SCSS 变量在编译时能利用 `base.css` 中的 CSS 变量，以更好地实现动态主题切换。
    *   **超越 Element Plus 默认观感:** 在 Element Plus 基础上，通过更细致的样式调整（如自定义字体、图标风格、控件细节、阴影、边框等）和统一的品牌色应用，打造更具辨识度的 UniBabble 应用外观。
    *   **微交互与动效:** 为关键操作（如消息发送、新消息到达、界面元素显隐）添加平滑、自然的过渡动画，提升操作的愉悦感和界面的生动性。

2.  **布局与信息层级优化:**
    *   **`HomeView.vue` (准备阶段):** 初始的语言选择和“开始/加入聊天”区域可以设计得更引导用户，视觉上更吸引人。
    *   **`ChatRoom.vue` - 房间信息区:**
        *   此区域信息密度较高。考虑使用图标按钮替代部分文字按钮（如下载、分享），或将不常用的操作（如分享、下载、甚至翻译细节控制）收纳到“更多操作”（例如用一个省略号图标表示）的下拉菜单中，保持界面清爽。
        *   优化用户列表展示：可以考虑使用用户头像代替或补充文字，设计更紧凑的列表样式。
    *   **全局布局 (`main.css`):** 审视 `main.css` 中针对 `#app` 的 `max-width` 和大屏幕下的两列网格布局 (`grid-template-columns: 1fr 1fr;`) 是否完全适合聊天应用。聊天界面通常是单列撑满（在 `max-width` 内）。确保不同层级的布局规则（`body`, `#app`, `HomeView`, `ChatRoom`）协同工作，避免冲突。

3.  **核心聊天体验提升:**
    *   **`MessageList.vue` (消息列表):**
        *   **用户身份标识:** 用**用户名**替代用户ID。引入**用户头像**，能极大提升消息的可读性和亲切感。
        *   **消息状态细化:** 除了“重发”按钮，可以为消息增加更明确的发送状态指示（如：发送中、已发送、已送达，已读状态则依赖后端）。
        *   **消息内错误提示:** 取消被注释的 `EMessageType.ERROR` 消息模板的注释，并为其设计合适的显示样式，用于展示单条消息相关的错误（如翻译失败）。
        *   **历史消息加载:** 如果聊天记录可能很长，应考虑实现“向上滚动加载更多历史消息”的功能，避免一次性加载大量数据影响性能。
        *   **时间戳优化:** 优化时间戳的显示方式，例如，相近时间的消息可以聚合显示时间，或者默认隐藏，鼠标悬浮时显示。
    *   **`MessageInput.vue` (消息输入):**
        *   **多行文本输入:** 将 `el-input type="text"` 改为 `type="textarea"` 并配合 `autosize` 属性，允许用户输入多行文本，这对于聊天是基本需求。同时需要明确换行和发送的交互（例如 Enter 发送，Shift+Enter 换行）。
        *   **输入增强:** 考虑添加“一键清空输入内容”的按钮、表情符号选择器等常见聊天辅助功能。
    *   **可读性与排版:** 仔细调整消息气泡内的字体、字号、行高、内外边距，确保在不同语言、不同长度的文本下都有良好的阅读体验。

4.  **用户流程与交互细节:**
    *   **`LanguageSelector.vue` 与语言设置流程:**
        *   明确用户在 `HomeView` 中通过 `LanguageSelector` 选择的语言的用途：是设定用户的全局语言偏好（影响 `currentUser.lang`），还是仅为当前聊天会话设定目标翻译语言或源语言？这需要与 `chatStore` 中的状态管理逻辑对应清晰。
        *   `LanguageSelector` 根据目标语言代码推断源语言的逻辑 (`newCode.slice(0, 2)`) 需要确认其健壮性，并考虑是否应允许用户独立设置源语言和目标语言。
        *   为 `el-select` 添加明确的 `placeholder`，如“请选择您的语言”。
    *   **“分享聊天记录”功能:** 若要启用，需设计清晰的入口和交互流程。
    *   **反馈一致性与明确性:**
        *   确保所有异步操作都有统一的加载状态指示。
        *   错误提示信息应尽可能具体、友好，并指导用户如何解决。
        *   成功操作的反馈（如“房间链接已复制”）是好的实践，可以推广到更多操作。

5.  **响应式设计与可访问性 (A11y):**
    *   **全面响应式:** 除了现有的少量媒体查询，需系统性测试和优化应用在各种屏幕尺寸（从小手机到大桌面）下的显示效果和可用性。特别关注 `ChatRoom.vue` 的信息栏和消息输入区在小屏设备上的表现。
    *   **可访问性增强:**
        *   严格检查并确保所有自定义组件和交互元素都符合 WCAG 标准，例如颜色对比度、焦点管理、ARIA属性的正确使用（特别是对于动态更新的内容如连接状态、新消息提示等）。
        *   保证完善的键盘导航功能。

6.  **性能优化 (UI层面):**
    *   **虚拟滚动:** 对于 `MessageList.vue`，如果预计会有非常长的聊天记录，应考虑引入虚拟滚动技术，只渲染视口内可见的消息项，以保持界面流畅。
    *   **资源优化:** 若引入图片（如头像），需进行适当优化。
    *   这些与 `MVP_PLAN.md` 中提到的“性能优化”阶段目标（如首屏加载、资源优化）相符。

7.  **代码与资源清理:**
    *   移除生产环境中不必要的 `console.log` 语句。
    *   检查是否有未使用的CSS类、变量或组件。

## 开发时间规划

### 第一周：基础设施 ✅
- [x] 天1-2：项目初始化和路由设计
- [x] 天3-4：状态管理实现
- [x] 天5：基础UI组件搭建

### 第二周：后端服务 📝
- [ ] 天1-2：后端服务基础架构搭建
- [ ] 天3-4：后端服务核心功能实现
- [ ] 天5：后端服务API设计

### 第三周：前端核心功能 🔄
- [ ] 天1-2：WebSocket 客户端实现
- [ ] 天3：状态管理更新
- [ ] 天4-5：UI 组件搭建

### 第四周：功能整合 📝
- [ ] 天1-2：消息流程实现
- [ ] 天3：会话管理
- [ ] 天4-5：用户体验优化

### 第五周：测试和部署 📝
- [ ] 天1-2：单元测试编写
- [ ] 天3：集成测试
- [ ] 天4：性能优化
- [ ] 天5：部署准备

## 验证标准

### 1. 功能验证
- 消息能够正确发送和接收
- 翻译功能准确可用
- 会话管理正常工作

### 2. 性能验证
- 消息延迟 < 1s
- 翻译延迟 < 2s
- 首屏加载 < 3s

### 3. 可用性验证
- 断线重连成功率 > 95%
- 翻译成功率 > 98%
- 核心功能可用性 > 99%

## 进度标记说明
- ✅ 已完成
- 🔄 进行中
- 📝 待开始

## 当前状态
- 基础设施搭建已完成
- 正在进入后端服务实现阶段
- 下一步：开始实现 WebSocket 客户端或翻译服务

## 风险评估

1. **技术风险**
   - WebSocket 连接稳定性
   - 翻译API的可用性和成本
   - 浏览器兼容性问题

2. **用户体验风险**
   - 翻译延迟影响对话流畅度
   - 网络状况对实时通讯的影响
   - 用户语言偏好设置的准确性

3. **运维风险**
   - 服务器负载能力
   - 数据安全性
   - 系统可扩展性

## 里程碑

1. **Alpha 版本** 🔄
   - [x] 基础框架搭建完成
   - [ ] 基础聊天功能可用
   - [ ] 翻译功能集成完成
   - [ ] 核心功能测试通过

2. **Beta 版本** 📝
   - [ ] 用户体验优化完成
   - [ ] 性能指标达标
   - [ ] 文档完善

3. **MVP 发布** 📝
   - [ ] 所有核心功能稳定运行
   - [ ] 测试用例覆盖完整
   - [ ] 部署文档完备

## 技术实现细节

### 后端服务 (MVP 范围)

1. **服务器配置**
   ```typescript
   const express = require('express');
   const WebSocket = require('ws');
   const app = express();
   const wss = new WebSocket.Server({ port: 8080 });
   ```

2. **房间管理**
   ```typescript
   const rooms = new Map<string, Room>();
   ```

3. **消息流程**
   - 接收消息
   - 确定目标语言
   - 调用翻译服务
   - 转发翻译后的消息

4. **错误处理**
   - 连接异常
   - 翻译服务异常
   - 消息发送失败

### 前端实现 (MVP 范围)

1. **WebSocket 连接**
   ```typescript
   class WebSocketService {
     private ws: WebSocket | null = null;
     private roomId: string | null = null;

     connect(roomId: string) {
       this.roomId = roomId;
       this.ws = new WebSocket(`ws://localhost:8080`);
       this.setupListeners();
     }
   }
   ```

2. **状态管理**
   ```typescript
   interface ChatState {
     status: 'connected' | 'disconnected' | 'connecting';
     messages: BasicMessage[];
     currentRoom: string | null;
   }
   ```

3. **UI 交互**
   - 消息发送/接收
   - 翻译状态显示
   - 连接状态提示

## 客户端消息管理优化计划

### 一、MVP阶段核心功能

1. **基础消息管理**
   - [ ] 简单的会话级消息存储
   - [ ] 基本的内存控制（限制消息数量）
   - [ ] 会话结束时的清理机制

2. **基础可靠性保证**
   - [ ] WebSocket连接状态监控
   - [ ] 简单的重连机制
   - [ ] 基本的消息确认机制

3. **用户体验保证**
   - [ ] 连接状态显示
   - [ ] 错误提示
   - [ ] 基本的加载状态

4. **基础监控**
   - [ ] 基本性能指标收集
   - [ ] 错误日志记录
   - [ ] 用户反馈机制

### 二、待优化项目

1. **错误处理优化**
   - 网络突然断开的恢复机制
   - 消息发送失败的重试策略
   - 服务器无响应的处理流程

2. **边界情况处理**
   - 大量消息积累时的性能优化
   - 频繁重连的智能退避策略
   - 并发消息的有序处理

3. **用户体验优化**
   - 更友好的加载状态反馈
   - 更详细的错误提示信息
   - 操作响应的及时性优化

4. **监控和调试增强**
   - 扩展关键指标收集
   - 完善错误追踪机制
   - 增强性能监控能力

### 三、优化时间规划

1. **短期优化（1-2周）**
   - 消息压缩机制
   - 批量处理优化
   - 错误处理增强

2. **中期优化（2-4周）**
   - 消息重传机制
   - 智能内存管理
   - 性能优化

3. **长期优化（1-2月）**
   - 分布式支持
   - 高级消息队列
   - 完整监控系统

### 四、优化优先级

1. **高优先级**
   - 🔴 基本的消息可靠性
   - 🔴 核心错误处理
   - 🔴 关键用户体验

2. **中优先级**
   - 🟡 性能优化
   - 🟡 监控系统
   - 🟡 高级错误处理

3. **低优先级**
   - 🟢 高级特性
   - 🟢 分布式支持
   - 🟢 深度优化
