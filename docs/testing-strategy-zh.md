# UniBabble 测试策略

## 目录

1. [测试概览](#测试概览)
2. [测试金字塔](#测试金字塔)
3. [测试工具栈](#测试工具栈)
4. [单元测试](#单元测试)
   - [Store 测试](#store-测试)
   - [组合式函数测试](#组合式函数测试)
   - [工具函数测试](#工具函数测试)
5. [组件测试](#组件测试)
6. [集成测试](#集成测试)
7. [E2E 测试](#e2e-测试)
8. [WebSocket 测试](#websocket-测试)
9. [测试覆盖率](#测试覆盖率)
10. [CI/CD 集成](#cicd-集成)
11. [性能测试](#性能测试)
12. [测试数据管理](#测试数据管理)
13. [测试命名约定](#测试命名约定)
14. [代码审查清单](#代码审查清单)

## 测试概览

UniBabble 是一个基于 WebSocket 的实时聊天应用，采用前后端分离架构。本测试策略旨在确保应用的功能性、可靠性和性能。

## 测试金字塔

```
                  /
                 /  \
                /    \
               / E2E  \
              /________\
             /          \
            / Integration \
           /______________\
          /                \
         /     Unit         \
        /____________________\
```

- **单元测试**: 70% 的测试覆盖率
- **集成测试**: 20% 的测试覆盖率
- **E2E 测试**: 10% 的测试覆盖率

## 测试工具栈

| 测试类型       | 工具                                                                 |
| -------------- | -------------------------------------------------------------------- |
| 单元测试       | Vitest, Vue Test Utils, @testing-library/vue                       |
| 组件测试       | Vitest, Vue Test Utils, @testing-library/vue, @testing-library/user-event |
| 集成测试       | Vitest, MSW (Mock Service Worker)                                   |
| E2E 测试       | Cypress                                                            |
| 覆盖率         | Vitest (内置)                                                      |
| 快照测试       | Vitest                                                             |
| 性能测试       | tinybench                                                          |
| API 测试       | Supertest                                                          |
| WebSocket 测试 | socket.io-mock, Mock-Socket                                        |


## 单元测试

### Store 测试

#### 测试目标
- 状态管理逻辑
- Actions/Mutations 行为
- Getters 计算属性

#### 示例: useChatStore 测试

```typescript
// tests/unit/stores/useChatStore.test.ts
import { setActivePinia, createPinia } from 'pinia'
import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { useChatStore } from '@/stores/useChatStore'
import { EChatModeType, WebSocketEvents } from '@unibabble/shared'

describe('useChatStore', () => {
  let store: ReturnType<typeof useChatStore>
  
  beforeEach(() => {
    setActivePinia(createPinia())
    store = useChatStore()
  })

  describe('random matching', () => {
    it('should handle successful random match ack', async () => {
      // 模拟 WebSocket 事件
      const mockData = {
        ack: 'CONFIRM',
        roomId: 123,
        isInvitor: true,
        userToken: 'test-token-123'
      }
      
      // 模拟 socket.emit 方法
      const mockEmit = vi.fn()
      const mockOn = vi.fn((event, callback) => {
        if (event === WebSocketEvents.ROOM.RANDOM.ACK) {
          callback(mockData)
        }
      })
      
      // 模拟 socket 实例
      vi.mock('@/services/socket', () => ({
        getSocket: () => ({
          on: mockOn,
          emit: mockEmit,
          connected: true
        })
      }))
      
      // 执行匹配
      await store.matchRandom()
      
      // 验证状态更新
      expect(store.randomRoomId).toBe(123)
      expect(mockEmit).toHaveBeenCalledWith(WebSocketEvents.ROOM.RANDOM, {
        sourceLang: store.selectedSourceLangCode,
        targetLang: store.selectedTargetLangCode
      })
    })
    
    it('should handle random match failure', async () => {
      // 测试错误处理逻辑
    })
  })
  
  // 其他测试用例...
})
```

### 组合式函数测试

#### 测试目标
- 组合式函数的输入输出
- 响应式行为
- 生命周期钩子

#### 示例

```typescript
// tests/unit/composables/useWebSocket.test.ts
import { ref } from 'vue'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useWebSocket } from '@/composables/useWebSocket'

describe('useWebSocket', () => {
  it('should connect to WebSocket server', async () => {
    const { isConnected, connect } = useWebSocket()
    
    await connect()
    
    expect(isConnected.value).toBe(true)
  })
  
  // 测试消息发送和接收
  // 测试错误处理
  // 测试重连逻辑
})
```

### 工具函数测试

#### 测试目标
- 纯函数的输入输出
- 边界条件
- 错误处理

#### 示例

```typescript
// tests/unit/utils/validation.test.ts
import { validateUsername } from '@/utils/validation'

describe('validateUsername', () => {
  it('should accept valid usernames', () => {
    expect(validateUsername('user123')).toBe(true)
    expect(validateUsername('user_name-123')).toBe(true)
  })
  
  it('should reject invalid usernames', () => {
    expect(validateUsername('ab')).toBe(false) // 太短
    expect(validateUsername('a'.repeat(33))).toBe(false) // 太长
    expect(validateUsername('user@name')).toBe(false) // 无效字符
  })
})
```

## 组件测试

### 测试策略
- 渲染测试
- 用户交互测试
- 属性传递测试
- 插槽测试
- 事件发射测试

### 示例: ChatRoom 组件测试

```typescript
// tests/components/ChatRoom.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import ChatRoom from '@/components/ChatRoom.vue'
import { useChatStore } from '@/stores/useChatStore'

describe('ChatRoom', () => {
  let wrapper: ReturnType<typeof mount>
  let store: ReturnType<typeof useChatStore>
  
  const createComponent = () => {
    return mount(ChatRoom, {
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn,
          stubActions: false
        })]
      },
      props: {
        roomId: 'test-room-123'
      }
    })
  }
  
  beforeEach(() => {
    setActivePinia(createPinia())
    store = useChatStore()
    wrapper = createComponent()
  })
  
  it('renders messages', async () => {
    // 设置测试消息
    store.messages = new Map([
      ['msg1', { id: 'msg1', content: 'Hello', sender: 'user1', timestamp: Date.now() }],
      ['msg2', { id: 'msg2', content: 'Hi there!', sender: 'user2', timestamp: Date.now() }]
    ])
    
    await nextTick()
    
    const messages = wrapper.findAll('.message')
    expect(messages).toHaveLength(2)
    expect(messages[0].text()).toContain('Hello')
    expect(messages[1].text()).toContain('Hi there!')
  })
  
  it('sends a message when form is submitted', async () => {
    const messageInput = wrapper.find('input[type="text"]')
    const form = wrapper.find('form')
    
    await messageInput.setValue('New message')
    await form.trigger('submit')
    
    expect(store.sendMessage).toHaveBeenCalledWith('New message', 'test-room-123')
  })
  
  // 测试 WebSocket 消息接收
  // 测试离开房间
  // 测试错误处理
})
```

## 集成测试

### 测试策略
- 组件与 store 的集成
- 多个组件的交互
- API 调用和状态更新

### 示例: 用户认证流程

```typescript
// tests/integration/authFlow.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi, beforeAll, afterEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import { setupServer } from 'msw/node'
import { rest } from 'msw'
import App from '@/App.vue'
import { useAuthStore } from '@/stores/auth'

const server = setupServer(
  rest.post('/api/auth/login', (req, res, ctx) => {
    return res(
      ctx.json({ token: 'test-token', user: { id: '1', username: 'testuser' } })
    )
  })
)

describe('Authentication Flow', () => {
  beforeAll(() => {
    server.listen()
  })
  
  afterEach(() => {
    server.resetHandlers()
    localStorage.clear()
  })
  
  afterAll(() => {
    server.close()
  })
  
  it('logs in and redirects to chat', async () => {
    const wrapper = mount(App)
    const authStore = useAuthStore()
    
    // 初始在登录页面
    expect(wrapper.find('h1').text()).toBe('Login')
    
    // 填写登录表单
    await wrapper.find('input[type="text"]').setValue('testuser')
    await wrapper.find('input[type="password"]').setValue('password123')
    await wrapper.find('form').trigger('submit')
    
    // 等待异步操作完成
    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    // 验证状态更新和重定向
    expect(authStore.isAuthenticated).toBe(true)
    expect(wrapper.find('h1').text()).toBe('Chat Room')
  })
})
```

## E2E 测试

### 测试策略
- 关键用户流程
- 跨浏览器测试
- 真实环境验证

### 示例: 聊天流程测试

```typescript
// tests/e2e/chat.cy.ts
describe('Chat', () => {
  beforeEach(() => {
    cy.visit('/')
    // 登录
    cy.get('input[type="text"]').type('testuser')
    cy.get('input[type="password"]').type('password123')
    cy.get('form').submit()
    // 等待重定向
    cy.url().should('include', '/chat')
  })
  
  it('sends and receives messages', () => {
    const testMessage = 'Hello, Cypress!'
    
    // 发送消息
    cy.get('.message-input').type(testMessage)
    cy.get('.send-button').click()
    
    // 验证消息显示
    cy.get('.message-list').should('contain', testMessage)
    
    // 模拟接收消息
    cy.window().then((win) => {
      win.socket.emit('message', {
        id: 'msg2',
        content: 'Hi there!',
        sender: 'otheruser',
        timestamp: Date.now()
      })
    })
    
    // 验证接收到的消息
    cy.get('.message-list').should('contain', 'Hi there!')
  })
  
  // 测试离开房间
  // 测试多用户交互
  // 测试断线重连
})
```

## WebSocket 测试

### 测试策略
- 连接管理
- 消息收发
- 错误处理
- 重连逻辑

### 示例: WebSocket 连接测试

```typescript
// tests/unit/services/websocket.test.ts
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { io } from 'socket.io-client'
import { createSocketConnection, getSocket } from '@/services/websocket'

// Mock socket.io-client
vi.mock('socket.io-client', () => {
  const mockSocket = {
    on: vi.fn(),
    emit: vi.fn(),
    disconnect: vi.fn(),
    connected: false
  }
  
  return {
    io: vi.fn(() => {
      // 模拟连接成功
      setTimeout(() => {
        mockSocket.connected = true
        mockSocket.on('connect', () => {})
      }, 10)
      return mockSocket
    })
  }
})

describe('WebSocket Service', () => {
  let socket: ReturnType<typeof io>
  
  beforeEach(() => {
    socket = createSocketConnection('http://localhost:3000')
  })
  
  afterEach(() => {
    socket.disconnect()
    vi.clearAllMocks()
  })
  
  it('connects to WebSocket server', async () => {
    expect(socket).toBeDefined()
    expect(io).toHaveBeenCalledWith('http://localhost:3000', {
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      timeout: 20000
    })
  })
  
  it('sends and receives messages', async () => {
    const testMessage = { type: 'test', data: 'Hello' }
    const callback = vi.fn()
    
    // 模拟消息接收
    socket.on('message', callback)
    
    // 发送消息
    socket.emit('message', testMessage)
    
    // 验证发送
    expect(socket.emit).toHaveBeenCalledWith('message', testMessage)
    
    // 模拟服务器响应
    const response = { type: 'response', data: 'Received' }
    const messageHandler = socket.on.mock.calls.find(
      ([event]) => event === 'message'
    )?.[1]
    
    if (messageHandler) {
      messageHandler(response)
      expect(callback).toHaveBeenCalledWith(response)
    } else {
      throw new Error('Message handler not found')
    }
  })
  
  // 测试断线重连
  // 测试错误处理
})
```

## 测试覆盖率

### 配置

在 `vitest.config.ts` 中配置覆盖率报告：

```typescript
export default defineConfig({
  test: {
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        '**/node_modules/**',
        '**/dist/**',
        '**/*.d.ts',
        '**/*.test.{js,ts,jsx,tsx}',
        '**/__mocks__/**',
        '**/types/**',
        '**/index.ts',
        '**/main.ts',
        '**/vite-env.d.ts'
      ],
      thresholds: {
        lines: 80,
        functions: 80,
        branches: 80,
        statements: 80
      }
    }
  }
})
```

### 生成报告

```bash
# 运行测试并生成覆盖率报告
pnpm test:coverage

# 查看 HTML 报告
open coverage/index.html
```

## CI/CD 集成

### GitHub Actions 工作流

```yaml
name: Test

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis
        ports:
          - 6379:6379
      
    steps:
      - uses: actions/checkout@v3
      
      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install
        
      - name: Run client tests
        working-directory: packages/client
        run: pnpm test:ci
        
      - name: Run server tests
        working-directory: packages/server
        run: pnpm test:ci
        env:
          NODE_ENV: test
          REDIS_URL: redis://localhost:6379
      
      - name: Run E2E tests
        working-directory: packages/client
        run: pnpm test:e2e:ci
        
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          file: \
            ./packages/client/coverage/coverage-final.json\
            ./packages/server/coverage/coverage-final.json
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false
```

## 性能测试

### 使用 tinybench 进行基准测试

```typescript
// tests/benchmark/messageProcessing.bench.ts
import { Bench } from 'tinybench'
import { processMessage } from '@/utils/messageProcessor'

const bench = new Bench({ time: 100 })

bench
  .add('process short message', () => {
    processMessage('Hello')
  })
  .add('process long message', () => {
    processMessage('a'.repeat(1000))
  })
  .add('process message with emoji', () => {
    processMessage('Hello 😊')
  })

await bench.warmup()
await bench.run()

console.table(bench.table())
```

## 测试数据管理

### 工厂函数

```typescript
// tests/factories/userFactory.ts
export const createUser = (overrides: Partial<User> = {}): User => ({
  id: 'user-' + Math.random().toString(36).substr(2, 9),
  username: 'testuser',
  email: '<EMAIL>',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides
})

// 使用
const user = createUser({ username: 'customuser' })
```

### 测试数据

```typescript
// tests/__fixtures__/messages.ts
export const mockMessages = [
  {
    id: 'msg1',
    content: 'Hello',
    sender: 'user1',
    timestamp: Date.now() - 60000, // 1 minute ago
    roomId: 'room1'
  },
  // more messages...
]
```

## 测试命名约定

### 测试文件命名
- 单元测试: `[name].test.ts`
- 组件测试: `[ComponentName].test.ts`
- E2E 测试: `[feature].cy.ts`

### 测试描述结构

```typescript
describe('Module/Component Name', () => {
  describe('method/feature', () => {
    it('should do something when condition', () => {
      // test
    })
    
    it('should handle error case', () => {
      // test error handling
    })
  })
})
```

## 代码审查清单

### 测试代码质量
- [ ] 测试描述清晰表达意图
- [ ] 测试用例独立且可重复
- [ ] 避免测试实现细节
- [ ] 使用适当的断言
- [ ] 测试边界条件
- [ ] 测试错误场景

### 测试覆盖范围
- [ ] 核心业务逻辑
- [ ] 用户交互
- [ ] 错误处理
- [ ] 边缘情况
- [ ] 性能关键路径

### 测试维护
- [ ] 测试数据可维护
- [ ] 测试用例组织清晰
- [ ] 避免重复代码
- [ ] 使用辅助函数减少样板代码
- [ ] 定期更新过时的测试
