# UniBabble Vue 3 开发规范

## 目录

- [1. 组件设计规范](#1-组件设计规范)
- [2. 状态管理规范](#2-状态管理规范)
- [3. 组合式函数规范](#3-组合式函数规范)
- [4. TypeScript规范](#4-typescript规范)
- [5. 样式规范](#5-样式规范)
- [6. 性能优化规范](#6-性能优化规范)
- [7. 测试规范](#7-测试规范)
- [8. 文档规范](#8-文档规范)

## 1. 组件设计规范

### 1.1 组件命名

```typescript
// ✅ 推荐的命名
ChatMessage.vue;
UserAvatar.vue;
RoomList.vue;

// ❌ 避免的命名
Message.vue; // 过于宽泛
chat - message.vue; // 不使用 PascalCase
```

### 1.2 组件结构

```vue
<script setup lang="ts">
  // 1. 类型导入
  import type { Message } from '@/types';

  // 2. 组件导入
  import ChatBubble from './ChatBubble.vue';

  // 3. 组合式函数导入
  import { useChat } from '@/composables/useChat';

  // 4. Props 定义
  interface Props {
    message: Message;
    isOwn?: boolean;
  }
  const props = withDefaults(defineProps<Props>(), {
    isOwn: false,
  });

  // 5. Emits 定义
  const emit = defineEmits<{
    reply: [message: Message];
    delete: [id: string];
  }>();

  // 6. 响应式数据
  const isEditing = ref(false);

  // 7. 计算属性
  const messageClass = computed(() => ({
    'message--own': props.isOwn,
    'message--editing': isEditing.value,
  }));

  // 8. 生命周期钩子
  onMounted(() => {
    // ...
  });
</script>
```

### 1.3 组件通信规范

1. Props Down, Events Up 原则
2. 使用 `defineProps` 和 `defineEmits` 进行类型安全的通信
3. 复杂状态使用 Pinia 管理
4. 避免过深的组件嵌套（最多3层）

## 2. 状态管理规范

### 2.1 Pinia Store 结构

```typescript
// stores/chat.ts
export const useChatStore = defineStore('chat', () => {
  // 1. 状态定义
  const messages = ref<Message[]>([]);
  const currentRoom = ref<Room | null>(null);

  // 2. Getters
  const unreadCount = computed(() => messages.value.filter(m => !m.isRead).length);

  // 3. Actions
  function sendMessage(message: Message) {
    // ...
  }

  return {
    messages,
    currentRoom,
    unreadCount,
    sendMessage,
  };
});
```

### 2.2 状态管理原则

1. 局部状态用 `ref`/`reactive`
2. 跨组件状态用 Pinia
3. 临时UI状态放组件内
4. 业务数据状态放 Store

## 3. 组合式函数规范

### 3.1 基本结构

```typescript
// composables/useWebSocket.ts
export function useWebSocket(options: WebSocketOptions) {
  // 1. 响应式状态
  const isConnected = ref(false);
  const messages = ref<Message[]>([]);

  // 2. 方法定义
  function connect() {
    // ...
  }

  // 3. 生命周期处理
  onMounted(() => {
    connect();
  });

  onUnmounted(() => {
    disconnect();
  });

  // 4. 返回值
  return {
    isConnected,
    messages,
    send,
    disconnect,
  };
}
```

### 3.2 组合式函数原则

1. 单一职责
2. 可组合性
3. 响应式优先
4. 生命周期安全

## 4. TypeScript规范

### 4.1 类型定义

```typescript
// 1. 接口优于类型别名
interface User {
  id: string;
  name: string;
  avatar?: string;
}

// 2. 常量枚举
const enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  SYSTEM = 'system',
}

// 3. 工具类型
type MessagePayload<T> = {
  type: MessageType;
  content: T;
  timestamp: number;
};
```

### 4.2 类型使用原则

1. 所有 props 必须有类型
2. 避免使用 `any`
3. 优先使用 `interface`
4. 合理使用泛型

## 5. 样式规范

### 5.1 CSS 组织

```vue
<style scoped lang="scss">
  // 1. 变量引用
  @use '@/styles/variables' as *;

  // 2. BEM 命名
  .chat-message {
    &__content {
      // ...
    }

    &--own {
      // ...
    }
  }

  // 3. 响应式设计
  @media (max-width: $breakpoint-md) {
    .chat-message {
      // ...
    }
  }
</style>
```

### 5.2 样式原则

1. 使用 SCSS
2. 遵循 BEM 命名
3. 使用 CSS 变量
4. 移动优先响应式设计

## 6. 性能优化规范

### 6.1 代码层面

1. 使用 `shallowRef` 处理大数据
2. 合理使用 `v-memo` 缓存
3. 避免不必要的计算属性
4. 使用异步组件

```typescript
// 异步组件示例
const ChatRoom = defineAsyncComponent(() => import('./ChatRoom.vue'));
```

### 6.2 资源层面

1. 路由懒加载
2. 图片懒加载
3. 合理的缓存策略
4. 组件按需导入

## 7. 测试规范

### 7.1 单元测试

```typescript
// ChatMessage.spec.ts
describe('ChatMessage', () => {
  it('should render message content', () => {
    const message = {
      id: '1',
      content: 'Hello',
    };
    const wrapper = mount(ChatMessage, {
      props: { message },
    });
    expect(wrapper.text()).toContain('Hello');
  });
});
```

### 7.2 测试原则

1. 组件测试覆盖关键功能
2. Store 测试覆盖所有 action
3. 工具函数 100% 覆盖
4. E2E 测试覆盖主流程

## 8. 文档规范

### 8.1 组件文档

```typescript
/**
 * 聊天消息组件
 * @component ChatMessage
 * @example
 * <ChatMessage
 *   :message="message"
 *   :isOwn="true"
 *   @reply="handleReply"
 * />
 */
```

### 8.2 文档要求

1. 组件必须有注释
2. 复杂逻辑需要说明
3. API 变更要更新文档
4. 提供使用示例

## 最佳实践建议

1. **代码审查清单**

   - TypeScript 类型完整性
   - 组件结构符合规范
   - 样式遵循 BEM
   - 测试覆盖率达标
   - 文档完整性

2. **性能检查清单**

   - 组件是否需要异步加载
   - 大数据是否使用 shallowRef
   - 是否有不必要的计算
   - 缓存策略是否合理

3. **安全检查清单**

   - 用户输入验证
   - XSS 防护
   - CSRF 防护
   - 敏感信息处理

4. **可访问性清单**
   - 语义化 HTML
   - ARIA 属性
   - 键盘导航
   - 颜色对比度

## 版本记录

- v1.0.0 (2024-01-22)
  - 初始版本
  - 完整的Vue 3 + TypeScript开发规范
  - 包含组件、状态管理、样式等完整指南
