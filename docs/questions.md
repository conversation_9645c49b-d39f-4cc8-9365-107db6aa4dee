server:
/*
1, 当心跳检测不到时, 将用户从房间退出 - OK
2, 当房间最新活动时间据当前>最大闲置时间时, 提示用户1分钟后房间关闭 - OK
3, 去掉服务端历史消息, 由客户端保存消息 - OK
4, * 统计这个room中的ack数, 也就是有多少用户收到了消息, 没有收到的是否有重试机制?
5, ** 客户端闲置超时后, 不退出房间, 更换连接状态
7, ** 重新连接后, 尝试重新进入/创建原房间, 继续聊天
6, * 添加客户端聊天记录下载功能 - ok
7, ws消息加密
8, BUG?? reenter 后, 会导致同房间的adminId变化, 变为先reenter的人, 边缘情况, 暂不考虑
.. NestJS 迁移
*/


client:

2.7 解决err负载逻辑
2.8 解决 start/join 返回的异常 且 不进入 chatroom
2.9 解决 client/server 异常处理流程, 异常提醒流程, quit流程, 发送失败的消息重试
2.10 优化 socket 系统事件
2.11 优化 timer 逻辑
2.12 完成 系统事件, 客户端断连http请求退出room
2.14 reenter各种状态, leave, disconnect
2.15 消息列表分享-ok, 消息列表下载-ok
2.16 翻译, mistral-ok, google, deepL

2.17 删除LanguageStore, leaveRoom后重选语言的用户语种更新问题; 翻译token数返回给server; 
2.24 google auth client ok; server ok
.. 接入cloudflare 真人验证
.. ack 状态, 发送方是否接收自己已发送的消息

优化消息列表的翻译状态: translating -> 翻译内容
用户google登录后,就连接到服务器,保持connect的状态, 远程可以发送邀请, 通过email来发送站内邀请加入房间, 测试: 添加login来模拟, 
join/start检查用户是否初始化
减小lang的体积, 使用枚举

-----------------
短链, 路径加密


---------------------
leaveRoom 后, 语种更新及显示, 翻译请求异常 ok
不更新socket, ok
用户离开/进入, 推送系统消息-ok
房间管理: 创建者, 踢人, 权限转移, 
翻译失败自动重试-ok
消息列表显示消息格式, 保持原样

用 wrk 或 autocannon 模拟server端翻译请求 5000 RPS 
多deepl key分担
后端状态可视化
server的用户保存, 好友列表



////////////////////////////
初期: 引入广告, 用户反馈, 来优化功能
