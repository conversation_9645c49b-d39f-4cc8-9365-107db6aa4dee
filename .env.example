# 应用配置
NODE_ENV=development
PORT=3000
HOST=localhost

# 前端配置
VITE_APP_TITLE=UniBabble
VITE_WS_URL=ws://localhost:3000/ws
VITE_API_URL=http://localhost:3000/api

# Mistral API配置
MISTRAL_API_KEY=your_api_key_here
MISTRAL_API_URL=https://api.mistral.ai/v1
MISTRAL_MODEL=mistral-tiny


# WebSocket配置
WS_PATH=/ws
WS_PING_INTERVAL=30000
WS_PING_TIMEOUT=5000
WS_MAX_HISTORY_SIZE=100
WS_MAX_MESSAGE_LENGTH=5000
WS_MESSAGE_RATE_LIMIT=60
WS_TRANSLATION_TIMEOUT=10000
WS_MESSAGE_RETRY_ATTEMPTS=3
WS_MESSAGE_RETRY_DELAY=1000

# 日志配置
LOG_LEVEL=debug

# CORS 配置
CORS_ORIGIN=http://localhost:5173