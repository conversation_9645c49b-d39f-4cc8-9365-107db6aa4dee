{"version": "2.0.0", "tasks": [{"label": "build:shared", "type": "shell", "command": "npx", "args": ["pnpm", "build:shared"], "group": "build", "presentation": {"reveal": "silent"}, "problemMatcher": "$tsc"}, {"label": "build:server", "type": "shell", "command": "npx", "args": ["pnpm", "build:server"], "group": "build", "presentation": {"reveal": "silent"}, "problemMatcher": "$tsc"}, {"label": "build:client", "type": "shell", "command": "npx", "args": ["pnpm", "build:client"], "group": "build", "presentation": {"reveal": "silent"}, "problemMatcher": "$tsc"}, {"label": "tsc-watch:server", "type": "shell", "command": "npx", "args": ["tsc", "-p", "${workspaceFolder}/packages/server/tsconfig.json", "--watch", "--preserveWatchOutput", "--sourceMap"], "group": "build", "isBackground": true, "problemMatcher": {"base": "$tsc-watch", "background": {"activeOnStart": true, "beginsPattern": "Starting compilation in watch mode...", "endsPattern": "Found 0 errors"}}, "presentation": {"reveal": "never", "panel": "dedicated"}}]}