{"npm.packageManager": "pnpm", "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "volar.takeOverMode.enabled": true, "volar.completion.preferredTagNameCase": "pascal", "volar.completion.preferredAttrNameCase": "camel", "volar.inlayHints.eventArgumentInInlineHandlers": false, "IDX.aI.enableInlineCompletion": true, "IDX.aI.enableCodebaseIndexing": true}