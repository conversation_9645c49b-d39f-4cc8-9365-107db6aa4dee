{"version": "0.2.0", "configurations": [{"name": "Backend", "type": "node", "request": "launch", "skipFiles": ["<node_internals>/**"], "runtimeExecutable": "npx", "runtimeArgs": ["tsx", "watch", "--inspect", "--require", "tsconfig-paths/register", "${workspaceFolder}/packages/server/src/index.ts"], "restart": true, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true, "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"], "cwd": "${workspaceFolder}/packages/server", "program": "${workspaceFolder}/packages/server/src/index.ts", "env": {"MODE": "dev", "DEBUG": "*", "NODE_PATH": "${workspaceFolder}/packages/server/node_modules"}, "presentation": {"hidden": false, "group": "1_debug", "order": 1}}, {"name": "Frontend", "type": "node", "request": "launch", "runtimeExecutable": "npx", "runtimeArgs": ["vite", "dev", "--mode", "dev", "--config", "${workspaceFolder}/packages/client/vite.config.ts", "--host", "--port", "5173", "--force"], "env": {"VITE_ENV": "dev"}, "cwd": "${workspaceFolder}/packages/client", "console": "integratedTerminal", "presentation": {"hidden": false, "group": "1_debug", "order": 2}}, {"name": "Backend remote", "type": "node", "request": "launch", "skipFiles": ["<node_internals>/**"], "runtimeExecutable": "npx", "runtimeArgs": ["tsx", "watch", "--inspect", "--require", "tsconfig-paths/register", "${workspaceFolder}/packages/server/src/index.ts"], "restart": true, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true, "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"], "cwd": "${workspaceFolder}/packages/server", "program": "${workspaceFolder}/packages/server/src/index.ts", "env": {"MODE": "remote", "DEBUG": "*", "NODE_PATH": "${workspaceFolder}/packages/server/node_modules"}, "presentation": {"hidden": false, "group": "1_debug", "order": 3}}, {"name": "Frontend remote", "type": "node", "request": "launch", "runtimeExecutable": "npx", "runtimeArgs": ["vite", "dev", "--config", "${workspaceFolder}/packages/client/vite.config.ts", "--host", "--mode", "remote", "--port", "5173", "--force"], "env": {"VITE_ENV": "remote"}, "cwd": "${workspaceFolder}/packages/client", "console": "integratedTerminal", "presentation": {"hidden": false, "group": "1_debug", "order": 4}}, {"name": "Chrome Attach", "type": "chrome", "request": "attach", "port": 9222, "address": "localhost", "url": "http://***************:5173", "webRoot": "${workspaceFolder}/packages/client", "sourceMaps": true, "sourceMapPathOverrides": {"/@fs/*": "${workspaceFolder}/*", "/src/*": "${workspaceFolder}/packages/client/src/*"}, "trace": true, "restart": true, "urlFilter": "****************:5173*", "presentation": {"hidden": false, "group": "1_debug", "order": 5}}, {"name": "Build Shared", "type": "node", "request": "launch", "runtimeExecutable": "npx", "runtimeArgs": ["pnpm", "build:shared"], "presentation": {"hidden": false, "group": "build", "order": 1}}, {"name": "Build Backend", "type": "node", "request": "launch", "runtimeExecutable": "npx", "runtimeArgs": ["pnpm", "build:server"], "presentation": {"hidden": false, "group": "build", "order": 2}}, {"name": "Build Frontend", "type": "node", "request": "launch", "runtimeExecutable": "npx", "runtimeArgs": ["pnpm", "build:client"], "presentation": {"hidden": false, "group": "build", "order": 3}}, {"name": "Build All", "type": "node", "request": "launch", "runtimeExecutable": "npx", "runtimeArgs": ["pnpm", "build"], "presentation": {"hidden": false, "group": "build", "order": 5}}], "compounds": [{"name": "Full Stack", "configurations": ["Backend", "Frontend", "Chrome Attach"], "stopAll": true, "presentation": {"hidden": false, "group": "3_debug", "order": 1}}, {"name": "Frontend Backend", "configurations": ["Backend", "Frontend"], "stopAll": true, "presentation": {"hidden": false, "group": "3_debug", "order": 2}}, {"name": "Vue With Chrome", "configurations": ["Frontend", "Chrome Attach"], "stopAll": true, "presentation": {"hidden": false, "group": "3_debug", "order": 3}}, {"name": "Build Frontend Backend", "configurations": ["Build Backend", "Build Frontend"], "stopAll": true, "presentation": {"hidden": false, "group": "build", "order": 4}}]}