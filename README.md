# UniBabble

If you like this project, Please offer me a cup of coffee. 
USDT Solona: GTBJRsWNyvWHkADdnjFwWuFxsagjXQLofYFdEqtmmsbz

UniBabble is a modern, real-time multilingual chat platform designed for seamless communication across language barriers. Built with a robust monorepo architecture, it integrates a Vue 3 + Vite frontend, an Express-based backend with WebSocket support, and a shared module for type and utility reuse. UniBabble aims to empower global teams and communities to collaborate efficiently, no matter their native language.

## Key Features

- **Real-Time Multilingual Chat:** Instant messaging with automatic translation between multiple languages, powered by leading translation APIs (e.g., DeepL).
- **Modern Frontend:** Built with Vue 3 (Composition API), Vite, Pinia, and Element Plus for a fast, responsive, and maintainable user experience.
- **Robust Backend:** Express server with WebSocket support for real-time communication, room and user management, and secure API endpoints.
- **Shared Utilities:** Common types and utilities shared across frontend and backend to ensure consistency and reduce duplication.
- **Monorepo Management:** Efficient dependency and build management using pnpm workspaces.

## Architecture Overview

```
UniBabble/
├── packages/
│   ├── client/   # Vue 3 + Vite frontend
│   ├── server/   # Express backend with WebSocket
│   └── shared/   # Shared types and utilities
```

- **Client:** The frontend SPA, providing a modern chat interface, user management, and real-time updates.
- **Server:** Handles authentication, message routing, translation, and real-time socket communication.
- **Shared:** Contains common logic, types, and helpers used by both client and server.

## Getting Started

### Prerequisites
- [Node.js](https://nodejs.org/) >= 18.x
- [pnpm](https://pnpm.io/) >= 8.x

### Installation

```bash
pnpm install
```

### Development

- **Start frontend:**
  ```bash
  pnpm dev:client
  ```
- **Start backend:**
  ```bash
  pnpm dev:server
  ```

### Build for Production

```bash
pnpm build
```

## Usage

1. Configure environment variables as needed in `.env` files for both client and server.
2. Start both client and server in development mode.
3. Access the chat application via the frontend URL (default: http://localhost:5173).

## Directory Structure

- `packages/client` – Frontend application (Vue 3, Vite, Pinia, Element Plus)
- `packages/server` – Backend service (Express, WebSocket, DeepL integration)
- `packages/shared` – Shared types, utilities, and logic

## Contributing

Contributions are welcome! Please follow best practices and ensure code consistency across packages.

## License

This project is licensed under the MIT License.
