{"name": "unibabble-monorepo", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev:client": "npx pnpm --filter @unibabble/client dev", "dev:server": "NODE_ENV=development npx pnpm --filter @unibabble/server dev", "build:shared": "npx pnpm --filter @unibabble/shared build", "build:client": "npx pnpm --filter @unibabble/client build", "build:server": "npx pnpm --filter @unibabble/server build", "build": "npx pnpm -r build", "start:server": "NODE_ENV=prod npx pnpm --filter @unibabble/server start", "test": "npx pnpm -r test", "clean": "npx pnpm -r clean", "lint": "npx pnpm -r lint", "format": "npx prettier --write \"**/*.{ts,tsx,vue,js,jsx,json,md}\"", "debug:dev": "npx pnpm build:shared && npx pnpm dev:client", "debug:full": "npx pnpm build:shared && npx pnpm dev:client & npx pnpm dev:server"}, "dependencies": {"axios": "1.7.9", "dotenv": "16.4.7", "socket.io": "4.8.1", "socket.io-client": "4.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@mswjs/interceptors": "^0.39.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1", "@testing-library/vue": "^8.1.0", "@types/node": "22.13.0", "@vitest/coverage-v8": "^3.2.2", "eslint": "9.21.0", "globals": "^16.1.0", "jsdom": "^26.1.0", "msw": "^2.10.1", "prettier": "3.5.1", "rimraf": "^5.0.5", "typescript": "5.7.3"}, "packageManager": "pnpm@10.11.0", "pnpm": {"ignoredBuiltDependencies": ["@parcel/watcher", "esbuild", "vue-demi"]}}