{"name": "@unibabble/server", "version": "0.0.1", "description": "UniBabble server - Real-time multilingual chat server", "private": true, "type": "module", "scripts": {"dev": "npx tsx watch src/index.ts", "build": "<PERSON>raf dist && tsc && tsc-alias", "start": "node dist/index.js", "debug:dev": "MODE=dev npx tsx watch --inspect --import tsx/esm --require tsconfig-paths/register src/index.ts", "debug:remote": "MODE=remote npx tsx watch --inspect --require tsconfig-paths/register src/index.ts"}, "dependencies": {"@unibabble/shared": "workspace:*", "cors": "2.8.5", "deepl-node": "^1.16.0", "express": "4.21.2", "google-auth-library": "^9.15.1"}, "devDependencies": {"@types/cors": "2.8.5", "@types/express": "4.17.21", "tsc-alias": "1.8.10", "tsconfig-paths": "4.2.0"}}