import { config as dotenv } from 'dotenv';
import {
  type ServerConfig, type OAuthConfig, type LogConfig, type BaseServiceConfig, type MessageConfig, type RoomConfig,
  type ServerConnectionConfig, type CorsConfig, type LLMApiConfig, type DeepLApiConfig,
  envFileMap,
  type ChatConfig
} from '@unibabble/shared';


const envFile = envFileMap[process.env.MODE];
dotenv({ path: envFile });


// 基础配置
export const serConfig: ServerConfig = {
  env: process.env.MODE,
  // common
  log: {
    level: process.env.SERVER_LOG_LEVEL || 'INFO',
  } as LogConfig,
  auth: {
    enable: process.env.SERVER_AUTH_ENABLE?.toLowerCase() === 'true',
    google: {
      clientId: process.env.SERVER_GOOGLE_CLIENT_ID || ''
    }
  } as OAuthConfig,
  server: {
    host: process.env.SERVER_HOST || 'localhost',
    port: process.env.SERVER_PORT || 3000,
    ws: process.env.SERVER_WS_PATH || '/unibabble.io',
    api: process.env.SERVER_API_PATH || '/api',
  } as BaseServiceConfig,

  chat: {
    perInviteTimeout: process.env.SERVER_CHAT_PER_INVITE_TIMEOUT || 13000,
    randomInviteCount: process.env.SERVER_CHAT_RANDOM_INVITE_COUNT || 5,
  } as ChatConfig,

  connection: {
    pingInterval: process.env.SERVER_PING_INTERVAL || 30000,
    pingTimeout: process.env.SERVER_PING_TIMEOUT || 90000,
  } as ServerConnectionConfig,

  cors: {
    origin: (process.env.SERVER_CORS_ORIGIN ?? '*').split(','),
    methods: (process.env.SERVER_CORS_METHODS ?? 'GET,POST,OPTIONS').split(','),
    allowedHeaders: (process.env.SERVER_CORS_HEADERS ?? 'Content-Type,Authorization').split(','),
  } as CorsConfig,

  translation: {
    mistral: {
      enable: process.env.SERVER_TRANSLATION_MISTRAL_ENABLE?.toLowerCase() === 'true',
      provider: 'mistral',
      baseUrl: process.env.SERVER_TRANSLATION_MISTRAL_BASE_URL || 'https://api.mistral.ai/v1/chat/completions',
      headers: {
        Authorization: `Bearer ${process.env.SERVER_TRANSLATION_MISTRAL_API_KEY}`,
        'Content-Type': 'application/json'
      },
      apiKey: process.env.SERVER_TRANSLATION_MISTRAL_API_KEY || '',
      model: process.env.SERVER_TRANSLATION_MISTRAL_MODEL || 'mistral-large-latest',
      timeout: process.env.SERVER_TRANSLATION_MISTRAL_TIMEOUT || 5000,
      maxTokens: process.env.SERVER_TRANSLATION_MISTRAL_MAXTOKENS || 8196,
      temperature: process.env.SERVER_TRANSLATION_MISTRAL_TEMPERATURE || 0.3,
    } as LLMApiConfig,
    deepl: {
      enable: process.env.SERVER_TRANSLATION_DEEPL_ENABLE?.toLowerCase() === 'true',
      provider: 'deepl',
      apiKey: process.env.SERVER_TRANSLATION_DEEPL_API_KEY || '',
      baseUrl: process.env.SERVER_TRANSLATION_DEEPL_BASE_URL || 'https://api.deepl.com',
      model: process.env.SERVER_TRANSLATION_DEEPL_MODEL || 'prefer_quality_optimized',
      headers: {
        Authorization: `DeepL-Auth-Key ${process.env.SERVER_TRANSLATION_DEEPL_API_KEY}`,
        'Content-Type': 'application/json'
      }
    } as DeepLApiConfig
  },

  message: {
    messageMaxLength: process.env.SERVER_MESSAGE_MAX_LENGTH || 1000,
    messageRateLimit: process.env.SERVER_MESSAGE_RATE_LIMIT || 60,
    messageSendInterval: process.env.SERVER_MESSAGE_SEND_INTERVAL || 1000,
    messageRetryAttempts: process.env.SERVER_MESSAGE_RETRY_ATTEMPTS || 3,
    messageRetryDelay: process.env.SERVER_MESSAGE_RETRY_DELAY || 1000,
  } as MessageConfig,

  room: {
    roomMaxHistorySize: process.env.SERVER_ROOM_MAX_HISTORY_SIZE || 100,
    roomMaxUsers: process.env.SERVER_ROOM_MAX_USERS || 2,
    roomMaxIdle: process.env.SERVER_ROOM_MAX_IDLE || 21600000,  // 6小时
    roomMaxIdleCheckInterval: process.env.SERVER_ROOM_MAX_IDLE_CHECK_INTERVAL || 60000, // 1小时
    roomForceDisconnectInterval: process.env.SERVER_ROOM_FORCE_DISCONNECT_INTERVAL || 60000,
    roomUserStatusCheckInterval: process.env.SERVER_ROOM_USER_STATUS_CHECK_INTERVAL || 60000,
    roomMaxCount: process.env.SERVER_ROOM_MAX_COUNT || 1000000,
  } as RoomConfig
};


// 导出安全相关配置
export const security = {
  jwtSecret: process.env.SERVER_JWT_SECRET || 'default-jwt-secret',
  rateLimit: process.env.SERVER_RATE_LIMIT || 1000,
  rateWindow: process.env.SERVER_RATE_WINDOW || 60000
};