import type { Room, User, CompactUserLang } from '@unibabble/shared';

/* 
room A : u1 lang1 usertoken1
room B : u1 lang2 usertoken2
Map<userBase, {lang, userToken, roomId}>
*/

// 统一的状态管理接口
export interface ServerState {
  roomStates: Map<number, RoomWithStatus>;
  userRoomMsgState: Map<string, Map<number, UserRoomState>>;  // userId -> <roomId -> state>
  users: Map<string, User>;
  randomUser: Map<string, UserInviteStatus>;

  // timestamp -> (path -> count)
  routeGetStatistic: Map<number, Map<string, number>>
  routePostStatistic: Map<number, Map<string, number>>
}

export interface UserRoomState {
  userToken: string;
  lang: CompactUserLang;
  msgCount: number;
  timestamp: number;
}

export interface RoomWithStatus extends Room {
  lastActiveAt: number;
  messageCounter: number;
  kickedIds: Set<string> // must be userId
  ackCount?: number
  adminToken: string
}

export type userRoomIdLangs = Map<number, CompactUserLang>

export interface UserWithStatus extends User {
  userToken: string;
}

export interface UserInviteStatus {
  lastInviteTime: number; // 上次邀请时间戳
  isInviting: boolean;    // 是否正在邀请中
}