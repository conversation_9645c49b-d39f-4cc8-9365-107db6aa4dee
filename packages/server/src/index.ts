import express from 'express';
import type { Express } from 'express';
import { createServer } from 'http';
import { serConfig } from '@/configs/index.js';
import { logger, getTimestamp } from '@unibabble/shared';
import { initializeWebSocketIO } from '@/ws.js';
import { startRoomStatusTimer, startUserStatusTimer, showServerStateStats } from '@/timers.js';
import { roomRoutes, translateRoutes } from '@/routes/index.js';
import cors from 'cors';
import type { ServerState, UserInviteStatus } from './types/index.js';
import { errorHandler, googleAuthMiddleware } from '@/middleware/index.js';


const MODULE_NAME = 'Server:index';

export const serverState: ServerState = {
    roomStates: new Map(),
    userRoomMsgState: new Map(),
    users: new Map(),
    randomUser: new Map<string, UserInviteStatus>(),
    routeGetStatistic: new Map(),
    routePostStatistic: new Map()
};


export const app: Express = express();
const httpServer = createServer(app);
export const io = initializeWebSocketIO(httpServer);

logger.debug(`process.env.SERVER_AUTH_ENABLE type: ${typeof process.env.SERVER_AUTH_ENABLE}`, {
    module: MODULE_NAME,
    method: 'start',
    timestamp: getTimestamp()
});
logger.debug(`process.env.SERVER_AUTH_ENABLE: ${process.env.SERVER_AUTH_ENABLE}`, {
    module: MODULE_NAME,
    method: 'start',
    timestamp: getTimestamp()
});
logger.debug(`serConfig: ${JSON.stringify(serConfig, null, 2)}`, {
    module: MODULE_NAME,
    method: 'start',
    timestamp: getTimestamp()
});

// middleware
app.use(cors(serConfig.cors)); // 允许跨域请求
app.use(express.json()); // 解析 JSON 请求体
if (serConfig.auth.enable) {
    io.use(googleAuthMiddleware);
}
app.use(errorHandler); // 错误处理中间件

// routes
app.use('/api/room', roomRoutes);
app.use('/api/translate', translateRoutes);


const port = serConfig.server.port;
httpServer.listen(port, () => {
    logger.info(`Server is running on port ${port}`, {
        module: MODULE_NAME,
        method: 'start',
        timestamp: getTimestamp()
    });
});

// timers
startRoomStatusTimer(io);
// startUserStatusTimer(io);
showServerStateStats();