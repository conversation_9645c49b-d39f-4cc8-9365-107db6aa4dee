import { serConfig } from '@/configs/index.js';
import { createSerErr, ErrorCode, getTimestamp, logger, unknown__serErr, type DeepLPostBody } from '@unibabble/shared';
import type { TranslateLang, TranslateResult, TranslateSupportLanguages, TranslateUsage } from '@unibabble/shared';
import { withRetry } from '@/utils/index.js';
import { Translator } from 'deepl-node';
import type { Language, TargetLanguageCode } from 'deepl-node';

const MODULE_NAME = 'Server:Http:PositiveRequest:DeepL'

const apiKey = serConfig.translation.deepl.apiKey


export const translate = async (body: DeepLPostBody): Promise<TranslateResult> => {

    const method = 'translate'
    const result: TranslateResult = {
        provider: 'deepl',
        content: body.content,
        translated: '',
        targetLang: body.receiverLangCode,
        sourceLang: body.senderLangCode,
    }
    
    try {
        if (!apiKey) {
            throw createSerErr(
                ErrorCode.API_DEEPL_KEY_NOT_CONFIGURED,
                'Invalid DeepL API key',
                'translate',
                MODULE_NAME
            )
        }

        const translator = new Translator(apiKey);

        // 使用重试机制调用翻译函数
        const res = await withRetry(async () => {
            const translation = await translator.translateText(
                body.content,
                null, // body.senderLangCode as deeplNode.SourceLanguageCode,
                body.receiverLangCode as TargetLanguageCode
            );
  
            if (!translation?.text) {
                throw createSerErr(
                    ErrorCode.API_DEEPL_RESPONSE_NO_CONTENT,
                    'No translations returned from DeepL',
                    method,
                    MODULE_NAME,
                    undefined,
                    undefined,
                    undefined,
                    { translation }
                );
            }
    
            return translation;
        }, method, MODULE_NAME);
        
        result.translated = res.text
    } catch (error) {
        logger.error('deepl translate failed', {
            timestamp: getTimestamp(),
            module: MODULE_NAME,
            method,
            error: unknown__serErr(error, ErrorCode.API_DEEPL_ERROR),
            details: { ...body }
        })
    }

    return result
}

export const checkUsage = async () => {
    const usage: TranslateUsage = {
        provider: 'deepl',
        completion_tokens: undefined,
        limit: undefined,
        anyLimitReached: undefined
    }

    try {
        if (!apiKey) {
            throw createSerErr(
                ErrorCode.API_DEEPL_KEY_NOT_CONFIGURED,
                'Invalid DeepL API key',
                'translate',
                MODULE_NAME
            )
        }

        const translator = new Translator(apiKey);
        const deepl_usage = await translator.getUsage();

        // /**
        //  * Stores the count and limit for one usage type.
        //  */
        // export interface UsageDetail {
        //     /** The amount used of this usage type. */
        //     readonly count: number;
        //     /** The maximum allowable amount for this usage type. */
        //     readonly limit: number;
        //     /**
        //      * Returns true if the amount used has already reached or passed the allowable amount.
        //      */
        //     limitReached(): boolean;
        // }
        // export interface Usage {
        //     /** Usage details for characters, for example due to the translateText() function. */
        //     readonly character?: UsageDetail
        //     /** Usage details for documents. */
        //     readonly document?: UsageDetail;
        //     /** Usage details for documents shared among your team. */
        //     readonly teamDocument?: UsageDetail;
        //     /** Returns true if any usage type limit has been reached or passed, otherwise false. */
        //     anyLimitReached(): boolean;
        //     /** Converts the usage details to a human-readable string. */
        //     toString(): string;
        // }

        const anyLimitReached = deepl_usage?.anyLimitReached();
        if (anyLimitReached != null) {
            usage.anyLimitReached = anyLimitReached;
        }

        if (deepl_usage.character) {
            usage.completion_tokens = deepl_usage.character.count
            usage.limit = deepl_usage.character.limit
        }

    } catch (error) {
        logger.error('check deepl usage failed', {
            timestamp: getTimestamp(),
            module: MODULE_NAME,
            method: 'checkUsage',
            details: { ...unknown__serErr(error, ErrorCode.API_DEEPL_ERROR) }
        })
    }

    return usage
}

export const checkTargetLangs = async () => {

    const supportedTargetLangs: TranslateSupportLanguages = {
        provider: 'deepl',
        langs: []
    }

    try {
        
        if (!apiKey) {
            throw createSerErr(
                ErrorCode.API_DEEPL_KEY_NOT_CONFIGURED,
                'Invalid DeepL API key',
                'translate',
                MODULE_NAME
            )
        }

        const translator = new Translator(apiKey);

        // export interface Language {
        //     /** Name of the language in English. */
        //     readonly name: string;
        //     /**
        //      * Language code according to ISO 639-1, for example 'en'. Some target languages also include
        //      * the regional variant according to ISO 3166-1, for example 'en-US'.
        //      */
        //     readonly code: LanguageCode;
        //     /**
        //      * Only defined for target languages. If defined, specifies whether the formality option is
        //      * available for this target language.
        //      */
        //     readonly supportsFormality?: boolean;
        // }

        const targetLanguages: readonly Language[] = await translator.getTargetLanguages();
        if (!targetLanguages) {
            throw createSerErr(
                ErrorCode.API_DEEPL_TARGET_LANGS_NO_RESPONSE,
                'Invalid DeepL target languages',
                'translate',
                MODULE_NAME
            )
        }
        
        supportedTargetLangs.langs = targetLanguages.map(lang => {
            return {
                source: lang.code.split('-')[0],
                target: lang.code,
                name: lang.name
            } as TranslateLang
        })

    } catch (error) {
        logger.error('get deepl target languages failed', {
            timestamp: getTimestamp(),
            module: MODULE_NAME,
            method: 'checkTargetLangs',
            details: { ...unknown__serErr(error, ErrorCode.API_DEEPL_ERROR) }
        })
    }

    return supportedTargetLangs
}
