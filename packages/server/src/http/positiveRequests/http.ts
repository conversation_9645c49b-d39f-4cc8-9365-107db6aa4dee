import axios from 'axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import { ErrorCode, unknown__serErr, UnibabbleError } from '@unibabble/shared';


const MODULE_NAME = 'Server:positiveRequest'
/**
 * Axios 请求配置
 */
interface HttpConfig<T = any> extends AxiosRequestConfig<T> {
    retries?: number; // 重试次数
    retryDelay?: number; // 重试延迟（毫秒）
}

/**
 * HTTP 错误类型
 */
interface HttpError extends Error {
    status?: number;
    data?: unknown;
}


/**
 * 发送 GET 请求
 * @param url - 请求 URL
 * @param config - 请求配置
 * @returns 响应数据
 */
export const get = async <T = any, R = any>(url: string, config?: HttpConfig<T>): Promise<AxiosResponse<R> | UnibabbleError> => {
    try {
        return await axios.get(url, config);
    } catch (error) {
        return unknown__serErr(error, ErrorCode.HTTP_GET_ERROR);
    }
};

const defaultPostConfig = {
    headers: {
        'Content-Type': 'application/json',
        Authorization: `DeepL-Auth-Key ${process.env.DEEPL_API_KEY}`
    }
}

const postBody__axiosConfig = <D>(body: D, config?: HttpConfig<D>): HttpConfig<D> => {
    return {
        ...config,
        ...defaultPostConfig,
        data: body
    }
}



/**
 * 发送 deeplPost 请求   
 * @param url - 请求 URL
 * @param data - 请求体数据
 * @param config - 请求配置
 * @returns 响应数据
 */
export const deeplPost = async <T = any, R = any>(url: string, data: T, config?: HttpConfig<T>): Promise<AxiosResponse<R, T> | UnibabbleError> => {
    try {
        config = postBody__axiosConfig(data, config);
        const rep = await axios.post<R, AxiosResponse<R, T>, T>(url, undefined, config);
        return rep;
    } catch (error) {
        return unknown__serErr(error, ErrorCode.HTTP_POST_ERROR);
    }
};


export const post = async <T = any, R = any>(url: string, data: T, config?: HttpConfig<T>): Promise<AxiosResponse<R, T> | UnibabbleError> => {
    try {
        return await axios.post<R, AxiosResponse<R, T>, T>(url, data, config);
    } catch (error) {
        return unknown__serErr(error, ErrorCode.HTTP_POST_ERROR);
    }
};