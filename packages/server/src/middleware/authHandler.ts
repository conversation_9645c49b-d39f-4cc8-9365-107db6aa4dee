import type { ServerSocket, User } from '@unibabble/shared';
import { OAuth2Client } from 'google-auth-library';
import type { ExtendedError } from 'socket.io';
import { serConfig } from '@/configs/index.js';

const client = new OAuth2Client(serConfig.auth.google.clientId, process.env.SERVER_GOOGLE_CLIENT_SECRET);

export const googleAuthMiddleware = async (socket: ServerSocket, next: (err?: ExtendedError) => void) => {
    if (serConfig.auth.enable) {
        const token = socket.handshake.auth.token;
        if (!token) return next(new Error('No token provided'));

        try {
            const ticket = await client.verifyIdToken({
                idToken: token,
                audience: serConfig.auth.google.clientId,
            });
            const payload = ticket.getPayload();
            if (!payload) return next(new Error('Invalid Google token'));
            const userName = payload.name ?? payload.family_name ?? payload.given_name ?? payload.email ?? 'GoogleUser';
            socket.data.userId = payload?.sub; // Google 用户 ID
            socket.data.userName = userName;
            socket.data.email = payload?.email;
            next();
        } catch (err) {
            next(new Error('Invalid Google token'));
        }
    } else {
        next();
    }
}