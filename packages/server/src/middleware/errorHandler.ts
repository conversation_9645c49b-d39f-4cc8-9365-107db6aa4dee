import type { Request, Response, NextFunction } from 'express';
import { logger, getTimestamp, ErrorCode, HttpStatusCode, type ApiResponse, createSerErr, UnibabbleError, unknown__serErr } from '@unibabble/shared';
import { METHODS } from 'http';

const MODULE_NAME = 'Server:middleware:errorHandler';

export const errorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {

    if (!err) {
        return next();
    }

    const method = 'errorHandler';
    
    logger.error('Server error', {
        module: MODULE_NAME,
        method: req.method,
        error: unknown__serErr(err),
        timestamp: getTimestamp(),
        details: { 
            body: req.body,
            params: req.params,
            query: req.query,
            a: req.baseUrl,
            h: req.headers,
            p: req.path,
            m: req.method,
            i: req.ip,
            c: req.errored
         }
    });

    res.status(HttpStatusCode.INTERNAL_SERVER_ERROR).json({
        status: HttpStatusCode.INTERNAL_SERVER_ERROR,
        code: ErrorCode.UNKNOWN_ERROR,
        data: unknown__serErr(err, ErrorCode.UNKNOWN_ERROR, method, MODULE_NAME)
    } as ApiResponse<UnibabbleError>);
}
