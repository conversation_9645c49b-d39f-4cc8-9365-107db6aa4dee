import type { EnvType } from '@unibabble/shared';

export declare global {
  namespace NodeJS {
    interface ProcessEnv {
      MODE: EnvType;

      // 服务器基础配置
      SERVER_HOST?: string;
      SERVER_PORT?: number;
      SERVER_WS_PATH?: string;
      SERVER_API_PATH?: string;

      // CORS配置
      SERVER_CORS_ORIGIN?: string;
      SERVER_CORS_METHODS?: string;
      SERVER_CORS_HEADERS?: string;

      // WebSocket配置
      SERVER_PING_INTERVAL?: number;
      SERVER_PING_TIMEOUT?: number;

      // 日志配置
      SERVER_LOG_LEVEL?: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';

      // 安全配置
      SERVER_JWT_SECRET?: string;
      SERVER_RATE_LIMIT?: number;
      SERVER_RATE_WINDOW?: number;
      SERVER_AUTH_ENABLE: string;
      SERVER_GOOGLE_CLIENT_ID?: string;

      // msg
      SERVER_MESSAGE_MAX_LENGTH?: number;
      SERVER_MESSAGE_RATE_LIMIT?: number;
      SERVER_MESSAGE_SEND_INTERVAL?: number;
      SERVER_MESSAGE_RETRY_ATTEMPTS?: number;
      SERVER_MESSAGE_RETRY_DELAY?: number;

      // room
      SERVER_ROOM_MAX_HISTORY_SIZE?: number;
      SERVER_ROOM_MAX_USERS?: number;
      SERVER_ROOM_MAX_IDLE?: number;
      SERVER_ROOM_MAX_IDLE_CHECK_INTERVAL?: number;
      SERVER_ROOM_FORCE_DISCONNECT_INTERVAL?: number;
      SERVER_ROOM_USER_STATUS_CHECK_INTERVAL?: number;
      SERVER_ROOM_MAX_COUNT?: number;

      // mistral
      SERVER_TRANSLATION_MISTRAL_ENABLE?: string;
      SERVER_TRANSLATION_MISTRAL_API_KEY?: string;
      SERVER_TRANSLATION_MISTRAL_BASE_URL?: string;
      SERVER_TRANSLATION_MISTRAL_MODEL?: string;
      SERVER_TRANSLATION_MISTRAL_TIMEOUT?: number;
      SERVER_TRANSLATION_MISTRAL_MAXTOKENS?: number;
      SERVER_TRANSLATION_MISTRAL_TEMPERATURE?: number;

      // deepl
      SERVER_TRANSLATION_DEEPL_ENABLE?: string;
      SERVER_TRANSLATION_DEEPL_API_KEY?: string;
      SERVER_TRANSLATION_DEEPL_BASE_URL?: string;
    }
  }
}
