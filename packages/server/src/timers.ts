import { serConfig } from "@/configs/index.js";
import { roomManager, serverState, userManager } from "@/ws.js";
import {
  getTimestamp, createSystemMessagePayload, logger,
  WebSocketEvents, RoomActiveStatus, unknown__serErr, findSocketByUserId
} from '@unibabble/shared'
import type { TranslateLang, RoomUpdatedPayload, ServerSocket } from "@unibabble/shared";
import type { Server } from 'socket.io';
import { emitToRoom } from "./utils/reponse.js";
import axios from 'axios';


// 存储所有定时器的引用
const intervals: NodeJS.Timeout[] = [];

const MODULE_NAME = 'Server:timer'

logger.info(`'最大空闲时间: ${serConfig.room.roomMaxIdle} ms, 空闲房间扫描间隔: ${serConfig.room.roomMaxIdleCheckInterval} ms, 强制离线间隔: ${serConfig.room.roomForceDisconnectInterval} ms'`, {
  module: MODULE_NAME,
  timestamp: getTimestamp()
})

/* 
房间长时间静置, 检查是否有用户在线, 如果没有, 清理房间, 使用户下线, 清理userMessageRates
*/
// 房间状态检查定时器
export function startRoomStatusTimer(io: Server) {
  const checkRoomStatus = () => {
    serverState.roomStates.forEach((room, roomId) => {
      const timestamp = getTimestamp()
      const warnLine = timestamp - serConfig.room.roomMaxIdle
      const deadLine = timestamp - serConfig.room.roomMaxIdle - serConfig.room.roomForceDisconnectInterval

      if (room.lastActiveAt <= deadLine && room.status === RoomActiveStatus.IDLE) {
        const sysMsg = createSystemMessagePayload(
          `The room is closed, You can re-enter.`,
          roomId
        );
        emitToRoom(io, roomId, WebSocketEvents.ACTION.SERVER.FORCEDISCONNECT, sysMsg);

        room.users.forEach(async user => {
          const socket = await findSocketByUserId(io, user.id, roomId)
          socket?.leave(roomId.toString());
          userManager.deleteUser(user.id);
        });

        roomManager.deleteRoom(roomId);
      } else if (room.lastActiveAt <= warnLine && room.lastActiveAt > deadLine) {

        // 即将超时，发送警告
        const content = `The room is about to close due to IDLE more than ${serConfig.room.roomMaxIdle / 1000 / 60} minutes`;
        io.to(roomId.toString()).emit(WebSocketEvents.WARNNING.ROOM.IDLE, createSystemMessagePayload(content, roomId));

        roomManager.updateRoom(room, {
          status: RoomActiveStatus.IDLE
        }, false)
      }
    });
  };

  const interval = setInterval(checkRoomStatus, serConfig.room.roomMaxIdleCheckInterval);
  intervals.push(interval);
  return interval;
}


/* 
如果用户断连, 短时间无法再次连接, 或者是客户端的transport error, 这样, 服务器如何识别这个用户的状态, 
用户断网: 当可连时..., 服务端扫描失效, 清理房间, 间隔?
当前客户端ws连接失败: 当reconnect failed时通过api请求退出room,方便重新连接并join - ok

*/
export function startUserStatusTimer(io: Server) {
  const checkUserStatus = () => {
    // online sockets
    const onlineUserIds = Array.from(io.sockets.sockets.values()).map((socket: ServerSocket) => socket.data.userId).filter(Boolean);

    // recorded ID
    const registeredUserIds = [
      ...new Set([
        ...serverState.users.keys(),
        ...serverState.userRoomMsgState.keys()
      ])
    ];

    // unavailable userID 
    const invalidUserIds = registeredUserIds.filter(id => !onlineUserIds.includes(id));

    // clean unavailable userID 
    invalidUserIds.forEach(async userId => {
      const map = serverState.userRoomMsgState.get(userId);
      if (map) {
        map.forEach(async (_, k) => {
          const roomState = serverState.roomStates.get(k);

          if (roomState && roomState.status !== RoomActiveStatus.IDLE) {
            roomState.users = roomState.users.filter(u => u.id !== userId);
            serverState.roomStates.set(roomState.id, roomState);

            const socket = await findSocketByUserId(io, userId, roomState.id)
            socket?.leave(roomState.id.toString());
            // emit to room
            const rupl: RoomUpdatedPayload = {
              timestamp: getTimestamp(),
              roomId: roomState.id,
              users: roomState.users,
              status: roomState.status,
              lastActiveAt: roomState.lastActiveAt,
              createdAt: roomState.createdAt
            }
            emitToRoom(io, roomState.id, WebSocketEvents.ROOM.UPDATE, rupl);
          }
        })
      }

      userManager.deleteUser(userId);
    });
  };

  const interval = setInterval(checkUserStatus, serConfig.room.roomUserStatusCheckInterval);
  intervals.push(interval);
  return interval;
}


export function showServerStateStats() {
  // 对serverState数据进行统计并显示日志
  const checkServerState = () => {
    const stats = {
      // 统计房间数据
      rooms: {
        total: serverState.roomStates.size,
        active: Array.from(serverState.roomStates.values()).filter(r => r.status === RoomActiveStatus.ACTIVE).length,
        inactive: Array.from(serverState.roomStates.values()).filter(r => r.status === RoomActiveStatus.INACTIVE).length,
        totalMessages: Array.from(serverState.roomStates.values()).reduce((acc, r) => acc + r.messageCounter, 0),
        rooms: serverState.roomStates.values()
      },
      // 统计用户数据
      users: {
        total: serverState.users.size,
        random: serverState.randomUser.size,
        messageRates: serverState.userRoomMsgState
      }
    };

    logger.info('Server State Statistics', {
      module: MODULE_NAME,
      timestamp: getTimestamp(),
      details: {
        rooms: stats.rooms,
        users: stats.users
      }
    });
  }

  const interval = setInterval(checkServerState, 5000);
  intervals.push(interval);
  return interval;
}


// 清理所有定时器
export function clearAllTimers() {
  intervals.forEach(interval => clearInterval(interval));
  intervals.length = 0;
}

// 注册进程退出处理
process.on('SIGTERM', clearAllTimers);
process.on('SIGINT', clearAllTimers); 