import { getTimestamp, logger, unknown__serErr, type User } from '@unibabble/shared';
import { serverState } from '@/ws.js';
import type { UserWithStatus } from '@/types/index.js';

const MODULE_NAME = 'Server:utils:common'

// 重试机制配置
const MAX_RETRIES = 3; // 最大重试次数
const INITIAL_RETRY_DELAY = 1000; // 初始重试延迟（毫秒）
const BACKOFF_FACTOR = 2; // 退避因子，每次重试延迟时间乘以该因子

/**
 * 重试机制封装
 * @param fn 需要重试的函数
 * @param retries 剩余重试次数
 * @param delay 当前重试延迟时间
 * @returns Promise<T>
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  method?: string,
  module?: string,
  retries: number = MAX_RETRIES,
  delay: number = INITIAL_RETRY_DELAY,
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    if (retries <= 0) {
      throw error; // 重试次数用尽，抛出异常
    }

    // // 判断是否为可重试错误
    // const isRetryableError =
    //   error.code === ErrorCode.API_DEEPL_RESPONSE_NO_CONTENT || // 翻译结果为空
    //   error.code === ErrorCode.API_DEEPL_ERROR || // DeepL API 错误
    //   error.code === ErrorCode.NETWORK_ERROR; // 网络错误

    // if (!isRetryableError) {
    //   throw error; // 不可重试错误，直接抛出
    // }

    // 记录重试日志
    logger.warn(`Retrying in ${delay}ms... (${retries} retries left)`, {
      timestamp: getTimestamp(),
      module: module ?? MODULE_NAME,
      method: method ?? 'withRetry',
      error: unknown__serErr(error),
      details: {
        delay,
        retriesLeft: MAX_RETRIES - retries,
        totalRetries: MAX_RETRIES
      }
    });

    // 等待一段时间后重试
    await new Promise((resolve) => setTimeout(resolve, delay));
    return withRetry(fn, method, module, retries - 1, delay * BACKOFF_FACTOR);
  }
}