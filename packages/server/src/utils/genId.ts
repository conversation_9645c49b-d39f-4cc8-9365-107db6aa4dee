import { serConfig } from "@/configs/index.js";
import { serverState } from "@/ws.js";

const CHARS = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ';
const MAX_ATTEMPTS = 10;  // 最大重试次数

// 用于生成字符串ID
const generate = (length = 6) => {
  let result = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * CHARS.length);
    result += CHARS[randomIndex];
  }
  return result;
};

// 用于生成数字ID的计数器
let roomIdCounter = 1;

// 生成数字类型的roomId
export const genRoomId = (): number => {
  // 检查当前计数器值是否已被使用
  while (serverState.roomStates.has(roomIdCounter)) {
    // 如果ID已存在，增加计数器
    roomIdCounter++;

    // 如果计数器过大，可以考虑重置或采取其他策略
    // 这里我们设置一个较大的阈值，实际使用时可根据需求调整
    if (roomIdCounter > serConfig.room.roomMaxCount) {
      // 寻找最小的未使用ID
      roomIdCounter = findSmallestUnusedId();
    }
  }

  return roomIdCounter++;
};

// 寻找最小的未使用ID
const findSmallestUnusedId = (): number => {
  let id = 1;
  while (serverState.roomStates.has(id)) {
    id++;
  }
  return id;
};

// 生成用户Token仍然使用字符串
export const genUserToken = () => generate(8);