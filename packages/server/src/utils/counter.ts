export class AtomicCounter<K> {
  private counters: Int32Array;
  private keyIndexMap: Map<K, number>;
  private currentIndex: number;

  constructor(maxKeys = 1000) {
    this.counters = new Int32Array(new SharedArrayBuffer(maxKeys * 4));
    this.keyIndexMap = new Map();
    this.currentIndex = 0;
  }

  has(key: K): boolean {
    return this.keyIndexMap.has(key);
  }

  increment(key: K): number {
    let index = this.keyIndexMap.get(key);
    if (index === undefined) {
      index = this.currentIndex++;
      this.keyIndexMap.set(key, index);
    }
    return Atomics.add(this.counters, index, 1);
  }

  get(key: K): number {
    const index = this.keyIndexMap.get(key);
    if (index === undefined) return 0;
    return Atomics.load(this.counters, index);
  }

  // 手动清理特定key
  clear(key: K) {
    const index = this.keyIndexMap.get(key);
    if (index !== undefined) {
      Atomics.store(this.counters, index, 0);
      this.keyIndexMap.delete(key);
    }
  }
}

