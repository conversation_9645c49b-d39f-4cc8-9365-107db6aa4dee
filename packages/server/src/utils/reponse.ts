import { createErrorMessage, createSystemMessagePayload, ErrSource, findSocketByUserId } from '@unibabble/shared'
import type { ServerEmitEvents, ErrorCode, ServerSocket, ServerSocketData } from '@unibabble/shared'
import { Server, type RemoteSocket } from 'socket.io'

/**
 * 发送错误消息
 * @param socket 
 * @param errCode 
 * @param emitEvent 
 * @param errStr 
 * @param moduleName
 * @param roomId 
 * @param userId 
 * @param details 
 */
export const emitErr = (
    socket: ServerSocket,
    errCode: ErrorCode,
    emitEvent: keyof ServerEmitEvents,
    errStr: string,
    method?: string,
    moduleName?: string,
    roomId?: number,
    userId?: string,
    details?: Record<string, unknown>
) => {
    const payload = createErrorMessage(
        ErrSource.SERVER,
        errCode,
        errStr,
        method ?? 'emitErr',
        moduleName,
        roomId,
        userId,
        emitEvent,
        details
    )

    socket.emit(emitEvent, payload);
}

/**
 * 发送系统消息
 * @param socket 
 * @param emitEvent 
 * @param message 
 * @param roomId 
 * @param details 
 */
export const emitSys = (
    socket: ServerSocket,
    emitEvent: keyof ServerEmitEvents,
    message: string,
    roomId: number,
    details?: Record<string, unknown>
) => {
    const payload = createSystemMessagePayload(
        message,
        roomId,
        details
    )

    socket.emit(emitEvent, payload);
}

/**
 * 发送消息到指定房间
 * @param io 
 * @param roomId 
 * @param emitEvent 
 * @param payload 
 */
export const emitToRoom = <E extends keyof ServerEmitEvents>(
    io: Server,
    roomId: number,
    emitEvent: E,
    payload: ServerEmitEvents[E] extends (payload: infer P) => void ? P : never,
) => {
    io.to(roomId.toString()).emit(emitEvent, payload);
}

export const emitPayload = <E extends keyof ServerEmitEvents>(
    socket: ServerSocket | RemoteSocket<ServerEmitEvents, ServerSocketData>,
    emitEvent: E,
    payload: Parameters<ServerEmitEvents[E]>[0]
) => {
    socket.emit(emitEvent, ...([payload] as Parameters<ServerEmitEvents[E]>));
}

// /**
//  * 发送消息到指定用户
//  * @param io 
//  * @param targetUserId 
//  * @param emitEvent 
//  * @param payload 
//  */
// export const emitToUser = async <E extends keyof ServerEmitEvents> (
//     io: Server,
//     targetUserId: string,
//     emitEvent: E,
//     payload: Parameters<ServerEmitEvents[E]>[0],
//     errorCallBack?: () => void,
//     roomId?: string 
// ) => {
//     const { socket } = await findSocketByUserId(io, targetUserId, roomId);
//     if (!socket) {
//         if (errorCallBack) { errorCallBack() }
//         return;
//     }
//     socket.emit(emitEvent, ...([payload] as Parameters<ServerEmitEvents[E]>));
// }

