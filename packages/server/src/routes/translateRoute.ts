import { serverState } from '@/ws.js';
import { HttpStatusCode, UnibabbleError, ErrorCode, unknown__serErr, logger, getTimestamp } from '@unibabble/shared';
import type { ApiResponse, DeepLPostBody, TranslateLang, TranslateResult } from '@unibabble/shared';
import { Router } from 'express';
import { translate } from '@/http/positiveRequests/index.js';

const MODULE_NAME = 'Server:http:route:translateRoute'

const router = Router();

// 获取语种数据的路由
// router.get('/langs', (_req, res) => {
//     const apiRes: ApiResponse<TranslateLang[]> = {
//         status: HttpStatusCode.OK,
//         code: ErrorCode.OK,
//         data: serverState.sourceLangs
//     }

//     res.send(apiRes);
// });

// 译文翻译的路由
router.post('/', async (req, res) => {
    const body: DeepLPostBody = req.body;

    const apiRes: ApiResponse<TranslateResult | UnibabbleError> = {
        status: HttpStatusCode.OK,
        code: ErrorCode.OK,
        data: undefined,
    }

    try {
        apiRes.data = await translate(body);
    } catch (err) {
        const serErr = unknown__serErr(
            err,
            ErrorCode.TRANSLATION_FAILED,
            'route.post:/translate',
            MODULE_NAME
        )

        logger.error('', {
            timestamp: getTimestamp(),
            module: MODULE_NAME,
            method: 'route.post:/translate',
            error: serErr
        })

        apiRes.code = ErrorCode.TRANSLATION_FAILED
        apiRes.data = serErr
    }

    res.send(apiRes);
});


export const translateRoutes: Router = router;