import { Router } from 'express';
import { roomManager } from '@/ws.js';
import type { ApiResponse, RoomUpdatedPayload } from '@unibabble/shared';
import { HttpStatusCode, ErrorCode, WebSocketEvents } from '@unibabble/shared';
import { emitToRoom } from '@/utils/reponse.js';
import { io } from '@/index.js';

const router = Router();

// router.post('/leave', (req, res) => {

//     const { timestamp, userId, roomId } = req.body;
//     const apiRes = {
//         status: HttpStatusCode.OK,
//         code: ErrorCode.UNKNOWN_ERROR,
//         data: undefined
//     } as ApiResponse

//     try {
//         const errCode = roomManager.api_leaveRoom(userId, roomId);

//         if (errCode && errCode === ErrorCode.OK) {
//             const room = roomManager.getRoom(roomId);
//             if (room) {
//                 const rupl: RoomUpdatedPayload = {
//                     roomId,
//                     users: room.users,
//                     lastActiveAt: timestamp,
//                     createdAt: room.createdAt,
//                     timestamp,
//                     status: room.status
//                 }
//                 apiRes.code = ErrorCode.OK;
//                 apiRes.data = rupl

//                 emitToRoom(
//                     io,
//                     roomId,
//                     WebSocketEvents.ROOM.UPDATE,
//                     rupl,
//                 )
//             } else {
//                 apiRes.code = ErrorCode.ROOM_NOT_FOUND;
//             }
//         } else {
//             apiRes.code = errCode;
//         }

//         res.send(apiRes);
//     } catch (e) {
//         apiRes.data = e;
//         res.send(apiRes);
//     }
// });

// router.post('/isin', (req, res) => {

//     const { timestamp, userId, roomId } = req.body;
//     const apiRes: ApiResponse<undefined | unknown> = {
//         status: HttpStatusCode.OK,
//         code: ErrorCode.UNKNOWN_ERROR,
//         data: undefined
//     }

//     try {
//         const errCode = roomManager.api_isInRoom(userId, roomId);

//         if (errCode) {
//             apiRes.code = errCode;
//         }

//         res.send(apiRes);
//     } catch (e) {
//         apiRes.data = e;
//         res.send(apiRes);
//     }
// });


export const roomRoutes: Router = router;
