{"compilerOptions": {"tsBuildInfoFile": "./dist/.tsbuildinfo", "incremental": true, "target": "ES2024", "lib": ["ES2024"], "outDir": "./dist", "rootDir": "./src", "strict": true, "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noUnusedLocals": false, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noImplicitAny": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@unibabble/shared": ["../shared/dist"]}, "sourceMap": true, "declaration": true, "declarationMap": true, "allowJs": true, "checkJs": true, "verbatimModuleSyntax": true, "allowImportingTsExtensions": false}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist"], "references": [{"path": "../shared"}]}