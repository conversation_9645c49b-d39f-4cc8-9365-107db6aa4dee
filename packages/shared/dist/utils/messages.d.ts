import type { SystemMessage, ErrorPayload, SystemPayload } from '../types/index.js';
import { ErrSource, ErrorCode, ClientEmitEvents, ServerEmitEvents } from '../types/index.js';
export declare const unknown__Error: (err: unknown) => Error;
/**
 * 创建错误消息
 */
export declare function createErrorMessage(source: typeof ErrSource[keyof typeof ErrSource], code: ErrorCode, message: string, method?: string, moduleName?: string, roomId?: number, userId?: string, eventType?: keyof ServerEmitEvents | keyof ClientEmitEvents, details?: Record<string, unknown>): ErrorPayload;
/**
 * 创建通用错误消息
 */
export declare function createUnknownError(error: unknown, moduleName: string): ErrorPayload;
/**
 * 创建系统消息
 */
export declare function createSystemMessagePayload(message: string, roomId: number, details?: Record<string, unknown>): SystemPayload;
export declare const idSpliter = "^";
export declare const genUserMsgId: (roomId: number, userId: string) => string;
export declare const genSysMsgId: (roomId?: number) => string;
export declare const genErrMsgId: (roomId?: number, userId?: string) => string;
export declare const createSystemMessage: (message: string, roomId?: number) => SystemMessage;
//# sourceMappingURL=messages.d.ts.map