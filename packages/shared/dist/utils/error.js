import { createErrorMessage, getTimestamp, logger } from "../utils/index.js";
import { ErrSource, UnibabbleError, ErrorCode } from "../types/index.js";
// 客户端错误转换
export const createCliErr = (code, message, method, module, roomId, userId, eventType, details) => {
    return new UnibabbleError(ErrSource.CLIENT, code, message, method, module, roomId, userId, eventType, details);
};
export const unknown__cliErr = (err, code, method, module, msg) => {
    return err instanceof UnibabbleError ?
        err :
        new UnibabbleError(ErrSource.CLIENT, code ?? ErrorCode.UNKNOWN_ERROR, err.message ?? msg ?? 'unknown client error, SEE details', method, module, undefined, undefined, undefined, { err });
};
export const cliErr__errPayload = (err, method, module) => {
    const me = method ? method + ' - ' : err.method ? err.method + ' - ' : '';
    const mo = module ? module + ' - ' : err.module ? err.module + ' - ' : '';
    logger.error(`Error in ${me}${err.eventType} operation - Code: ${err.code}${err.roomId ? `, Room: ${err.roomId}` : ''}`, {
        module: mo,
        method: me,
        timestamp: getTimestamp(),
        details: err.details
    });
    return createErrorMessage(ErrSource.CLIENT, err.code, err.message, me, mo, err.roomId, err.userId, err.eventType, err.details);
};
// 服务器错误转换
export const createSerErr = (code, message, method, module, roomId, userId, eventType, details) => {
    return new UnibabbleError(ErrSource.SERVER, code, message, method, module, roomId, userId, eventType, details);
};
export const unknown__serErr = (err, code, method, module, msg) => {
    return err instanceof UnibabbleError ?
        err :
        createSerErr(code ?? ErrorCode.UNKNOWN_ERROR, err.message ?? msg ?? 'unknown server error', method, module, undefined, undefined, undefined, { allMsg: JSON.stringify(err) });
};
export const serErr__errPayload = (err, method, module) => {
    return createErrorMessage(ErrSource.SERVER, err.code, err.message, method ?? err.method, module ?? 'Server', err.roomId, err.userId, err.eventType, err.details);
};
//# sourceMappingURL=error.js.map