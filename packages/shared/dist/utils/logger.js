/**
 * 格式化日志消息
 */
const formatMessage = (level, message, context) => {
    const { module, method, messageId, roomId, userId, error, details } = context;
    // 基础信息
    const timestamp = new Date(context.timestamp).toISOString();
    const baseInfo = `[${timestamp}] [${level}] [${module}${method ? `::${method}` : ''}]`;
    // 上下文信息
    const contextInfo = [
        messageId && `MessageID: ${messageId}`,
        roomId && `RoomID: ${roomId}`,
        userId && `UserID: ${userId}`,
    ].filter(Boolean).join(' | ');
    // 错误信息
    const errorInfo = error ? `\nError: ${error.message}${error.stack ? `\nStack: ${error.stack}` : ''}` : '';
    // 元数据信息
    const detailsInfo = details ? `\nDetails: ${JSON.stringify(details, null, 2)}` : '';
    // 组合所有信息
    return [
        baseInfo,
        message,
        contextInfo,
        errorInfo,
        detailsInfo
    ].filter(Boolean).join(' ');
};
/**
 * 记录日志
 */
const log = (level, message, context) => {
    const formattedMessage = formatMessage(level, message, context);
    switch (level) {
        case 'DEBUG':
            console.debug(formattedMessage);
            break;
        case 'INFO':
            console.info(formattedMessage);
            break;
        case 'WARN':
            console.warn(formattedMessage);
            break;
        case 'ERROR':
            console.error(formattedMessage);
            break;
    }
};
/**
 * 日志记录器实例
 */
export const logger = {
    debug: (message, context) => log('DEBUG', message, context),
    info: (message, context) => log('INFO', message, context),
    warn: (message, context) => log('WARN', message, context),
    error: (message, context) => log('ERROR', message, context)
};
//# sourceMappingURL=logger.js.map