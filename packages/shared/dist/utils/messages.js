import { EMessageType, ErrSource, ErrorCode } from '../types/index.js';
import { getTimestamp } from './common.js';
export const unknown__Error = (err) => err instanceof Error ? err : new Error(JSON.stringify(err));
/**
 * 创建错误消息
 */
export function createErrorMessage(source, code, message, method, moduleName, roomId, userId, eventType, details) {
    const timestamp = getTimestamp();
    const errMsg = {
        id: genErrMsgId(roomId, userId),
        timestamp,
        content: message,
        code: code,
        type: EMessageType.ERROR
    };
    return {
        moduleName,
        eventType,
        timestamp,
        errMsg,
        roomId,
        userId,
        method,
        source,
        details
    };
}
/**
 * 创建通用错误消息
 */
export function createUnknownError(error, moduleName) {
    const err = unknown__Error(error);
    return createErrorMessage(ErrSource.DEFAULT, ErrorCode.UNKNOWN_ERROR, err.message, undefined, moduleName, undefined, undefined, undefined, { err });
}
/**
 * 创建系统消息
 */
export function createSystemMessagePayload(message, roomId, details) {
    const timestamp = getTimestamp();
    const sysMsg = {
        id: genSysMsgId(roomId),
        timestamp,
        content: message,
        type: EMessageType.SYSTEM
    };
    return {
        timestamp,
        sysMsg,
        roomId,
        details,
    };
}
export const idSpliter = '^';
// 生成user消息ID
export const genUserMsgId = (roomId, userId) => {
    if (!roomId) {
        throw new Error('roomId is required');
    }
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 4);
    return [roomId, timestamp, userId, random].join(idSpliter);
};
// 生成sys消息ID
export const genSysMsgId = (roomId) => {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 4);
    return [
        roomId ? roomId.toString() : '',
        timestamp,
        random
    ].filter(Boolean).join(idSpliter);
};
// 生成err消息ID
export const genErrMsgId = (roomId, userId) => {
    return [
        roomId ? roomId.toString() : '',
        userId ? userId : '',
        getTimestamp().toString(36),
        Math.random().toString(36).substring(2, 4)
    ].filter(Boolean).join(idSpliter);
};
export const createSystemMessage = (message, roomId) => {
    return {
        id: genSysMsgId(roomId),
        timestamp: getTimestamp(),
        content: message,
        type: EMessageType.SYSTEM
    };
};
//# sourceMappingURL=messages.js.map