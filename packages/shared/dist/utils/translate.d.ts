import { CompactUserLang, LanguageCode, LanguageSource, UserLang } from "../types/index.js";
export declare const UserLangUtils: {
    targetCount: 31;
    compact(lang: UserLang): CompactUserLang;
    expand(compact: CompactUserLang): UserLang;
    createDefault(): CompactUserLang;
    fromSourceAndTarget(source: LanguageSource, target: LanguageCode): CompactUserLang;
};
//# sourceMappingURL=translate.d.ts.map