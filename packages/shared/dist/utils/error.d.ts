import { ErrorPayload, ClientEmitEvents, ServerEmitEvents } from "../types/index.js";
import { UnibabbleError, ErrorCode } from "../types/index.js";
export declare const createCliErr: (code: ErrorCode, message: string, method?: string, module?: string, roomId?: number, userId?: string, eventType?: keyof ServerEmitEvents, details?: Record<string, unknown>) => UnibabbleError;
export declare const unknown__cliErr: (err: unknown, code?: ErrorCode, method?: string, module?: string, msg?: string) => UnibabbleError;
export declare const cliErr__errPayload: (err: UnibabbleError, method?: string, module?: string) => ErrorPayload;
export declare const createSerErr: (code: ErrorCode, message: string, method?: string, module?: string, roomId?: number, userId?: string, eventType?: keyof ClientEmitEvents, details?: Record<string, unknown>) => UnibabbleError;
export declare const unknown__serErr: (err: unknown, code?: ErrorCode, method?: string, module?: string, msg?: string) => UnibabbleError;
export declare const serErr__errPayload: (err: UnibabbleError, method?: string, module?: string) => ErrorPayload;
//# sourceMappingURL=error.d.ts.map