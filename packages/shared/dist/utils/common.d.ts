import { RemoteSocket, Server } from 'socket.io';
import type { ServerEmitEvents, ServerSocketData, UserId } from '../types/index.js';
import { LanguageCode, LanguageName } from '../types/index.js';
/**
 * 格式化时间戳为本地时间字符串
 * @param timestamp 时间戳
 * @param options 格式化选项
 */
export declare function formatTime(timestamp: number, options?: Intl.DateTimeFormatOptions): string;
/**
 * 获取当前时间戳
 */
export declare function getTimestamp(): number;
/**
 * 获取语言名称
 * @param langCode 语言代码
 * @returns 语言名称
 */
export declare function getLanguageName(langCode: LanguageCode): LanguageName;
/**
 * 根据用户 ID 和房间 ID 找到对应的 Socket
 * @param io
 * @param userId
 * @param roomId
 * @returns
 */
export declare const findSocketByUserId: (io: Server, userId: UserId, roomId?: number) => Promise<RemoteSocket<ServerEmitEvents, ServerSocketData> | undefined>;
export declare const uuid: () => string;
//# sourceMappingURL=common.d.ts.map