import { AvailableLanguages } from '../types/index.js';
import { v4 as uuidv4 } from 'uuid';
/**
 * 格式化时间戳为本地时间字符串
 * @param timestamp 时间戳
 * @param options 格式化选项
 */
export function formatTime(timestamp, options = {
    hour: '2-digit',
    minute: '2-digit'
}) {
    return new Date(timestamp).toLocaleTimeString(undefined, options);
}
/**
 * 获取当前时间戳
 */
export function getTimestamp() {
    return Date.now();
}
/**
 * 获取语言名称
 * @param langCode 语言代码
 * @returns 语言名称
 */
export function getLanguageName(langCode) {
    if (!AvailableLanguages.has(langCode)) {
        throw new Error(`Unsupported language code: ${langCode}`);
    }
    return AvailableLanguages.get(langCode);
}
/**
 * 根据用户 ID 和房间 ID 找到对应的 Socket
 * @param io
 * @param userId
 * @param roomId
 * @returns
 */
export const findSocketByUserId = async (io, userId, roomId) => {
    const sockets = (() => {
        if (roomId) {
            return io.in(roomId.toString()).fetchSockets();
        }
        else {
            return io.sockets.fetchSockets();
        }
    })();
    return (await sockets).find(s => s.data.userId === userId);
};
export const uuid = () => {
    return uuidv4();
};
//# sourceMappingURL=common.js.map