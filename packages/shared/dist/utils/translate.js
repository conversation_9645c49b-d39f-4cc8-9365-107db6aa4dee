import { deeplLangs } from "../types/index.js";
// 工具函数
export const UserLangUtils = {
    // 计算 deeplLangs 的长度，用于后续位运算
    targetCount: deeplLangs.length,
    // 压缩 UserLang 到一个数字
    compact(lang) {
        // 查找目标语言在 deeplLangs 中的索引
        const langIndex = deeplLangs.findIndex(item => item.target === lang.target &&
            item.code === lang.source &&
            item.originalLangName === lang.name);
        if (langIndex === -1) {
            throw new Error(`不支持的语言组合: ${lang.source}, ${lang.target}, ${lang.name}`);
        }
        return langIndex;
    },
    // 从数字解压缩成 UserLang
    expand(compact) {
        if (compact < 0 || compact >= deeplLangs.length) {
            throw new Error(`无效的紧凑语言索引: ${compact}`);
        }
        const langData = deeplLangs[compact];
        return {
            source: langData.code,
            target: langData.target,
            name: langData.originalLangName
        };
    },
    // 创建默认值 (美式英语)
    createDefault() {
        // 美式英语在 deeplLangs 中的索引
        const defaultLangIndex = deeplLangs.findIndex(item => item.target === 'EN-US' &&
            item.code === 'EN');
        return defaultLangIndex !== -1 ? defaultLangIndex : 0;
    },
    // 从 source 和 target 创建 CompactUserLang
    fromSourceAndTarget(source, target) {
        const langIndex = deeplLangs.findIndex(item => item.target === target &&
            item.code === source);
        if (langIndex === -1) {
            throw new Error(`不支持的语言组合: ${source}, ${target}`);
        }
        return langIndex;
    }
};
//# sourceMappingURL=translate.js.map