{"version": 3, "file": "concurrent.d.ts", "sourceRoot": "", "sources": ["../../src/types/concurrent.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgDG;AACH,qBAAa,aAAa,CAAC,CAAC,EAAE,CAAC;IAE7B,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAGtB;IAGH,OAAO,CAAC,QAAQ,CAAC,MAAM,CAKrB;IAGF,OAAO,CAAC,QAAQ,CAAC,OAAO,CAMtB;gBAEU,OAAO,GAAE;QACnB,YAAY,CAAC,EAAE,MAAM,CAAC;QACtB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,WAAW,CAAC,EAAE,MAAM,CAAA;KAChB;IAkBN;;;OAGG;IACH,OAAO,CAAC,eAAe;IAUvB;;;OAGG;YACW,kBAAkB;IA8BhC;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAM1B;;OAEG;YACW,eAAe;IAqB7B;;;;;;;OAOG;IACG,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAS1C;;;;;;;;OAQG;IACG,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,SAAS,CAAC;IAQzC;;;;;;;;OAQG;IACG,MAAM,CACV,GAAG,EAAE,CAAC,EACN,QAAQ,EAAE,CAAC,YAAY,EAAE,CAAC,GAAG,SAAS,KAAK,CAAC,GAC3C,OAAO,CAAC,CAAC,CAAC;IAYb;;;;;;;;;;;;;OAaG;IACG,WAAW,CACf,OAAO,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,GAAG,SAAS,KAAK,CAAC,CAAC,GAClD,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAiCrB;;;;;;;;OAQG;IACG,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;IAQnC;;;;;;;;OAQG;IACG,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;IAWtC;;;;;;;;;OASG;IACG,IAAI,IAAI,OAAO,CAAC,MAAM,CAAC;IAU7B;;;;;;;;;OASG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAS5B;;;;;;;;;OASG;IACH,UAAU,IAAI,QAAQ,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC;IAI3C;;;;;;;;OAQG;IACH,YAAY,IAAI,IAAI;CAKrB"}