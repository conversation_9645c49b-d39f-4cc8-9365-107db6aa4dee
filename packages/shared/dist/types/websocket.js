export const WebSocketEvents = {
    STANDARD: {
        SERVER: {
            //当一个新的客户端成功连接到服务端时触发。参数: socket: Socket, 代表与新连接的客户端的通信通道。可以通过 socket.id 获取客户端的唯一标识符。
            CONNECTION: 'connection', // 初始化客户端相关的状态、监听客户端事件、分配资源等。
            // 在 disconnect 事件之前触发，表示客户端即将断开连接。	参数 reason
            DISCONNECTING: 'disconnecting', // 进行最后的清理工作（此时连接可能仍然有效，但不保证消息能可靠送达）。
            // 客户端断开连接时触发。
            DISCONNECT: 'disconnect', // 清理客户端相关的资源、更新在线用户列表、通知其他客户端等。
        },
        CLIENT: {
            // 客户端成功连接到服务端时触发。
            CONNECT: 'connect', // 发送初始数据、开始监听服务端事件、更新 UI 状态等。
            // 客户端断开连接时触发。	参数 reason: string
            DISCONNECT: 'disconnect', // 清理客户端状态、显示断开连接提示、尝试重新连接（如果启用了自动重连）等。
            // 客户端连接到服务端失败时触发。 参数 error: Error
            CONNECT_ERROR: 'connect_error', // 显示连接错误信息、尝试重新连接（如果启用了自动重连）等。
            // 客户端正在尝试重新连接到服务端时触发（每次尝试都会触发）。 一般不与RECONNECTING同时出现 参数 attamptNum: number
            RECONNECT_ATTEMPT: 'reconnect_attempt', // 显示正在重连的提示、更新重连尝试次数等。
            // 与reconnect_attempt类似，但更早，表示客户端已经决定要进行重连，但还没开始尝试。参数 attamptNum: number
            RECONNECTING: 'reconnecting',
            // 客户端重新连接到服务端失败时触发。	参数 error: Error
            RECONNECT_ERROR: 'reconnect_error', // 显示重连错误信息、可能需要根据错误类型采取不同的处理措施。
            // 客户端在达到最大重连尝试次数后仍然无法连接到服务端时触发。
            RECONNECT_FAILED: 'reconnect_failed', // 显示重连失败的提示、停止自动重连、可能需要用户手动触发重连等。
            // 客户端成功重新连接到服务端时触发。
            RECONNECT: 'reconnect', // 恢复之前的状态、重新订阅事件、更新 UI 状态等。
            // 客户端收到服务端发送的 ping 心跳包时触发。
            PING: 'ping', // 通常无需手动处理，Socket.IO 内部会自动响应 pong。
            // 客户端收到服务端对客户端发送的 ping 的响应（pong）时触发。
            PONG: 'pong', // 通常无需手动处理
        },
    },
    ROOM: {
        UPDATE: 'room:update',
        JOIN: 'room:join',
        JOINED: 'room:joined',
        LEAVE: 'room:leave',
        LEFT: 'room:leaved',
        REENTER: 'room:reenter',
        RANDOM: {
            MATCH: 'room:random:match',
            ACK: 'room:random:ack'
        },
    },
    MESSAGE: {
        SEND: {
            SENDING: 'message:send',
            DELIVERED: 'message:delivered',
            ACK: 'message:ack',
        },
        HISTORY: {
            SHARE: 'message:history:share',
            OFFER: 'message:history:offer',
        },
        TRANSLATE: 'message:translate',
    },
    USER: {
        KICK: 'user:kick',
        KICKED: 'user:kicked',
    },
    WARNNING: {
        ROOM: {
            IDLE: 'warnning:room:idle',
        },
    },
    ACTION: {
        SERVER: {
            FORCEDISCONNECT: 'action:server:forceDisconnect',
            INVITE: 'action:server:invite'
        },
    },
    ERROR: {
        ROOM: {
            JOIN: 'error:room:join',
            FULL: 'error:room:full',
            LEAVE: 'error:room:leave',
            NOT_FOUND: 'error:room:not_found',
            kICK: 'error:room:kick',
        },
        MESSAGE: {
            SEND_FAILED: 'error:message:send_failed',
            DELIVERED_FAILED: 'error:message:delivered_failed',
        },
        USER: {
            CONNECT: 'error:user:connect',
            NOT_EXISTS: 'error:user:not_exists',
        },
        UNKNOWN: 'error:unknown',
        SERVER: {
            STATUS: 'error:server:status',
            MATCH_RANDOM: 'error:server:match_random'
        }
    },
    SYSTEM: 'system',
    SETTINGS: 'setting',
    SERVER: {
        STATUS: 'server:status'
    }
};
/**
 * 用户状态
 */
export var UserStatus;
(function (UserStatus) {
    UserStatus["ONLINE"] = "online";
    UserStatus["OFFLINE"] = "offline";
})(UserStatus || (UserStatus = {}));
//# sourceMappingURL=websocket.js.map