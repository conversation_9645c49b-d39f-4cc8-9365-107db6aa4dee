import { ErrorCode } from "./index.js";
export type UserLang = {
    source: LanguageSource;
    target: LanguageCode;
    name: LanguageName;
};
export declare enum UserRole {
    MEMBER = 0,
    ADMIN = 1
}
/**
 * 用户信息
 */
export interface User {
    id: string;
    name: string;
    lang: UserLang;
    email?: string;
}
export type UserId = User['id'];
export declare enum RoomActiveStatus {
    ACTIVE = 0,
    INACTIVE = 1,
    IDLE = 2,
    BLOCKED = 3
}
/**
 * 房间信息
 */
export interface Room {
    id: number;
    users: User[];
    createdAt: number;
    status: RoomActiveStatus;
    adminId: UserId;
}
/**
 * 基础消息接口
 */
export interface BaseMessage {
    /** 消息唯一标识 */
    id: string;
    /** 发送时间戳 */
    timestamp: number;
}
export declare enum EMessageType {
    TEXT = 0,
    SYSTEM = 1,
    ERROR = 2
}
export declare enum EChatModeType {
    NORMAL = 0,
    RANDOM = 1
}
export declare enum EServerAckType {
    CONFIRM = 0,
    REJECT = 1
}
/**
 * 文本消息
 */
export interface TextMessage extends BaseMessage {
    /** 消息类型 */
    type: EMessageType.TEXT;
    /** 消息内容 */
    content: string;
    /** 房间唯一标识 */
    roomId: number;
    /** 发送者信息 */
    sender: User;
}
/**
 * 系统消息
 */
export interface SystemMessage extends BaseMessage {
    /** 消息类型 */
    type: EMessageType.SYSTEM;
    /** 消息内容 */
    content: string;
    /** 消息语言 */
    lang?: LanguageCode;
}
/**
 * 错误消息
 */
export interface ErrorMessage extends BaseMessage {
    /** 消息类型 */
    type: EMessageType.ERROR;
    /** 消息内容 */
    content: string;
    /** 错误代码 */
    code: ErrorCode;
}
/**
 * 消息类型
 */
export type Message = TextMessageWithState | SystemMessage | ErrorMessage;
/**
 * 消息状态
 */
export declare enum EMessageState {
    SENDING = 0,
    DELIVERED = 1,
    ACK = 2,
    RESENDING = 3,
    FAILED = 4
}
export interface MessageState {
    state: EMessageState;
    stateAt?: number;
    fstCheckAt?: number;
}
/**
 * 带状态的消息
 */
export interface TextMessageWithState extends TextMessage {
    state: MessageState;
    /** 翻译后的消息内容 */
    translatedContent?: string;
    targetLang?: LanguageCode;
}
export type FstCheckAt = number;
export declare const deeplLangs: readonly [{
    readonly code: "BG";
    readonly target: "BG";
    readonly name: "Bulgarian";
    readonly originalLangName: "Bulgarian";
    readonly supports_formality: false;
}, {
    readonly code: "CS";
    readonly target: "CS";
    readonly name: "Czech";
    readonly originalLangName: "Czech";
    readonly supports_formality: false;
}, {
    readonly code: "DA";
    readonly target: "DA";
    readonly name: "Danish";
    readonly originalLangName: "Danish";
    readonly supports_formality: false;
}, {
    readonly code: "DE";
    readonly target: "DE";
    readonly name: "German";
    readonly originalLangName: "Deutsch";
    readonly supports_formality: true;
}, {
    readonly code: "EL";
    readonly target: "EL";
    readonly name: "Greek";
    readonly originalLangName: "Greek";
    readonly supports_formality: false;
}, {
    readonly code: "EN";
    readonly target: "EN-GB";
    readonly name: "English (British)";
    readonly originalLangName: "English (British)";
    readonly supports_formality: false;
}, {
    readonly code: "EN";
    readonly target: "EN-US";
    readonly name: "English (American)";
    readonly originalLangName: "English (American)";
    readonly supports_formality: false;
}, {
    readonly code: "ES";
    readonly target: "ES";
    readonly name: "Spanish";
    readonly originalLangName: "Español";
    readonly supports_formality: true;
}, {
    readonly code: "ET";
    readonly target: "ET";
    readonly name: "Estonian";
    readonly originalLangName: "Estonian";
    readonly supports_formality: false;
}, {
    readonly code: "FI";
    readonly target: "FI";
    readonly name: "Finnish";
    readonly originalLangName: "Finnish";
    readonly supports_formality: false;
}, {
    readonly code: "FR";
    readonly target: "FR";
    readonly name: "French";
    readonly originalLangName: "Français";
    readonly supports_formality: true;
}, {
    readonly code: "HU";
    readonly target: "HU";
    readonly name: "Hungarian";
    readonly originalLangName: "Hungarian";
    readonly supports_formality: false;
}, {
    readonly code: "ID";
    readonly target: "ID";
    readonly name: "Indonesian";
    readonly originalLangName: "Indonesian";
    readonly supports_formality: false;
}, {
    readonly code: "IT";
    readonly target: "IT";
    readonly name: "Italian";
    readonly originalLangName: "Italiano";
    readonly supports_formality: true;
}, {
    readonly code: "JA";
    readonly target: "JA";
    readonly name: "Japanese";
    readonly originalLangName: "日本語";
    readonly supports_formality: true;
}, {
    readonly code: "KO";
    readonly target: "KO";
    readonly name: "Korean";
    readonly originalLangName: "Korean";
    readonly supports_formality: false;
}, {
    readonly code: "LT";
    readonly target: "LT";
    readonly name: "Lithuanian";
    readonly originalLangName: "Lithuanian";
    readonly supports_formality: false;
}, {
    readonly code: "LV";
    readonly target: "LV";
    readonly name: "Latvian";
    readonly originalLangName: "Latvian";
    readonly supports_formality: false;
}, {
    readonly code: "NB";
    readonly target: "NB";
    readonly name: "Norwegian";
    readonly originalLangName: "Norwegian";
    readonly supports_formality: false;
}, {
    readonly code: "NL";
    readonly target: "NL";
    readonly name: "Dutch";
    readonly originalLangName: "Nederlands";
    readonly supports_formality: true;
}, {
    readonly code: "PL";
    readonly target: "PL";
    readonly name: "Polish";
    readonly originalLangName: "Polski";
    readonly supports_formality: true;
}, {
    readonly code: "PT";
    readonly target: "PT-BR";
    readonly name: "Portuguese (Brazilian)";
    readonly originalLangName: "Português (Brasileiro)";
    readonly supports_formality: true;
}, {
    readonly code: "PT";
    readonly target: "PT-PT";
    readonly name: "Portuguese (European)";
    readonly originalLangName: "Português (Europeu)";
    readonly supports_formality: true;
}, {
    readonly code: "RO";
    readonly target: "RO";
    readonly name: "Romanian";
    readonly originalLangName: "Romanian";
    readonly supports_formality: false;
}, {
    readonly code: "RU";
    readonly target: "RU";
    readonly name: "Russian";
    readonly originalLangName: "Русский";
    readonly supports_formality: true;
}, {
    readonly code: "SK";
    readonly target: "SK";
    readonly name: "Slovak";
    readonly originalLangName: "Slovak";
    readonly supports_formality: false;
}, {
    readonly code: "SL";
    readonly target: "SL";
    readonly name: "Slovenian";
    readonly originalLangName: "Slovenian";
    readonly supports_formality: false;
}, {
    readonly code: "SV";
    readonly target: "SV";
    readonly name: "Swedish";
    readonly originalLangName: "Swedish";
    readonly supports_formality: false;
}, {
    readonly code: "TR";
    readonly target: "TR";
    readonly name: "Turkish";
    readonly originalLangName: "Turkish";
    readonly supports_formality: false;
}, {
    readonly code: "UK";
    readonly target: "UK";
    readonly name: "Ukrainian";
    readonly originalLangName: "Ukrainian";
    readonly supports_formality: false;
}, {
    readonly code: "ZH";
    readonly target: "ZH-HANS";
    readonly name: "Chinese (simplified)";
    readonly originalLangName: "中文";
}];
export declare const AvailableLanguages: Map<string, string>;
export type LanguageCode = typeof deeplLangs[number]["target"];
export type LanguageName = typeof deeplLangs[number]["originalLangName"];
export type LanguageSource = typeof deeplLangs[number]["code"];
export type CompactUserLang = number;
//# sourceMappingURL=base.d.ts.map