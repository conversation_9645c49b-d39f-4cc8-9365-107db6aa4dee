/**
 * 高性能并发 Map 实现
 * 采用分段锁策略，优化并发性能
 *
 * 使用示例:
 * ```typescript
 * // 创建实例
 * const map = new ConcurrentMap<string, number>({
 *   segmentCount: 64,  // 更多的分段以支持更高并发
 *   lockTimeout: 500,  // 更短的超时时间
 *   maxRetries: 5      // 更多的重试次数
 * });
 *
 * // 基本操作
 * await map.set('key1', 100);
 * const value = await map.get('key1');
 *
 * // 原子更新
 * await map.update('key1', (current = 0) => current + 1);
 *
 * // 批量更新
 * const updates = new Map([
 *   ['key1', (v = 0) => v + 1],
 *   ['key2', (v = 0) => v + 2]
 * ]);
 * await map.batchUpdate(updates);
 *
 * // 监控性能
 * console.log(map.getMetrics());
 *
 * 使用场景:
 * 1. 小数据量，低并发：使用原生 Map
 * const simpleMap = new Map<string, number>();
 *
 * 2. 中等数据量，需要并发：使用较少分段
 * const mediumMap = new ConcurrentMap<string, number>({
 *   segmentCount: 16
 * });
 *
 * 3. 大数据量，高并发：使用默认配置或更多分段
 * const largeMap = new ConcurrentMap<string, number>({
 *   segmentCount: 64
 * });
 *
 * ```
 *
 * @template K 键的类型
 * @template V 值的类型
 */
export class ConcurrentMap {
    // 分段存储
    segments;
    // 配置项
    config;
    // 性能指标
    metrics = {
        reads: 0,
        writes: 0,
        conflicts: 0,
        retries: 0,
        timeouts: 0
    };
    constructor(options = {}) {
        this.config = {
            segmentCount: options.segmentCount ?? 32, // 默认32个分段
            lockTimeout: options.lockTimeout ?? 1000, // 默认1秒超时
            maxRetries: options.maxRetries ?? 3, // 默认最多重试3次
            backoffBase: options.backoffBase ?? 10 // 默认10ms基础退避时间
        };
        // 初始化分段
        this.segments = Array.from({ length: this.config.segmentCount }, () => ({
            values: new Map(),
            lock: new Int32Array(new SharedArrayBuffer(4))
        }));
    }
    /**
     * 获取键对应的分段索引
     * 使用改进的哈希算法确保均匀分布
     */
    getSegmentIndex(key) {
        const str = String(key);
        let hash = 5381;
        for (let i = 0; i < str.length; i++) {
            hash = ((hash << 5) + hash) + str.charCodeAt(i);
            hash = hash & hash;
        }
        return Math.abs(hash) % this.config.segmentCount;
    }
    /**
     * 获取指定分段的锁
     * 使用指数退避策略
     */
    async acquireSegmentLock(segmentIndex, attempt = 0) {
        const segment = this.segments[segmentIndex];
        const startTime = Date.now();
        while (true) {
            // 尝试获取锁
            if (Atomics.compareExchange(segment.lock, 0, 0, 1) === 0) {
                return true;
            }
            // 检查是否超时
            if (Date.now() - startTime > this.config.lockTimeout) {
                this.metrics.timeouts++;
                return false;
            }
            // 计算退避时间
            const backoffTime = Math.min(this.config.backoffBase * Math.pow(2, attempt), 100 // 最大退避100ms
            );
            // 等待后重试
            await new Promise(resolve => setTimeout(resolve, backoffTime));
        }
    }
    /**
     * 释放分段锁
     */
    releaseSegmentLock(segmentIndex) {
        const segment = this.segments[segmentIndex];
        Atomics.store(segment.lock, 0, 0);
        Atomics.notify(segment.lock, 0, 1);
    }
    /**
     * 执行需要加锁的操作
     */
    async withSegmentLock(segmentIndex, operation) {
        let attempt = 0;
        while (attempt < this.config.maxRetries) {
            if (await this.acquireSegmentLock(segmentIndex, attempt)) {
                try {
                    return await operation();
                }
                finally {
                    this.releaseSegmentLock(segmentIndex);
                }
            }
            attempt++;
            this.metrics.retries++;
        }
        throw new Error(`无法获取分段 ${segmentIndex} 的锁`);
    }
    /**
     * 设置键值对
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * await map.set('counter', 1);
     * ```
     */
    async set(key, value) {
        const segmentIndex = this.getSegmentIndex(key);
        await this.withSegmentLock(segmentIndex, () => {
            this.segments[segmentIndex].values.set(key, value);
            this.metrics.writes++;
        });
        return this;
    }
    /**
     * 获取值
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * await map.set('counter', 1);
     * const value = await map.get('counter'); // 1
     * ```
     */
    async get(key) {
        const segmentIndex = this.getSegmentIndex(key);
        return this.withSegmentLock(segmentIndex, () => {
            this.metrics.reads++;
            return this.segments[segmentIndex].values.get(key);
        });
    }
    /**
     * 原子更新操作
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * // 原子递增
     * await map.update('counter', (current = 0) => current + 1);
     * ```
     */
    async update(key, updateFn) {
        const segmentIndex = this.getSegmentIndex(key);
        return this.withSegmentLock(segmentIndex, () => {
            const segment = this.segments[segmentIndex];
            const currentValue = segment.values.get(key);
            const newValue = updateFn(currentValue);
            segment.values.set(key, newValue);
            this.metrics.writes++;
            return newValue;
        });
    }
    /**
     * 批量更新操作
     * 按分段分组并顺序处理，避免死锁
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * // 批量更新多个计数器
     * const updates = new Map([
     *   ['counter1', (v = 0) => v + 1],
     *   ['counter2', (v = 0) => v + 2]
     * ]);
     * await map.batchUpdate(updates);
     * ```
     */
    async batchUpdate(updates) {
        // 按分段对更新分组
        const segmentGroups = new Map();
        for (const [key, updateFn] of updates) {
            const segmentIndex = this.getSegmentIndex(key);
            if (!segmentGroups.has(segmentIndex)) {
                segmentGroups.set(segmentIndex, new Map());
            }
            segmentGroups.get(segmentIndex).set(key, updateFn);
        }
        // 按分段顺序处理更新
        const results = new Map();
        const sortedSegments = Array.from(segmentGroups.keys()).sort();
        for (const segmentIndex of sortedSegments) {
            const segmentUpdates = segmentGroups.get(segmentIndex);
            await this.withSegmentLock(segmentIndex, () => {
                const segment = this.segments[segmentIndex];
                for (const [key, updateFn] of segmentUpdates) {
                    const currentValue = segment.values.get(key);
                    const newValue = updateFn(currentValue);
                    segment.values.set(key, newValue);
                    results.set(key, newValue);
                    this.metrics.writes++;
                }
            });
        }
        return results;
    }
    /**
     * 检查键是否存在
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * await map.set('key', 1);
     * const exists = await map.has('key'); // true
     * ```
     */
    async has(key) {
        const segmentIndex = this.getSegmentIndex(key);
        return this.withSegmentLock(segmentIndex, () => {
            this.metrics.reads++;
            return this.segments[segmentIndex].values.has(key);
        });
    }
    /**
     * 删除键值对
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * await map.set('temp', 1);
     * await map.delete('temp');
     * ```
     */
    async delete(key) {
        const segmentIndex = this.getSegmentIndex(key);
        return this.withSegmentLock(segmentIndex, () => {
            const result = this.segments[segmentIndex].values.delete(key);
            if (result) {
                this.metrics.writes++;
            }
            return result;
        });
    }
    /**
     * 获取当前大小
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * await map.set('key1', 1);
     * await map.set('key2', 2);
     * const size = await map.size(); // 2
     * ```
     */
    async size() {
        let total = 0;
        for (let i = 0; i < this.config.segmentCount; i++) {
            await this.withSegmentLock(i, () => {
                total += this.segments[i].values.size;
            });
        }
        return total;
    }
    /**
     * 清空 Map
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * await map.set('key', 1);
     * await map.clear();
     * const size = await map.size(); // 0
     * ```
     */
    async clear() {
        for (let i = 0; i < this.config.segmentCount; i++) {
            await this.withSegmentLock(i, () => {
                this.segments[i].values.clear();
                this.metrics.writes++;
            });
        }
    }
    /**
     * 获取性能指标
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * // 执行一些操作...
     * const metrics = map.getMetrics();
     * console.log(metrics.reads, metrics.writes);
     * ```
     */
    getMetrics() {
        return { ...this.metrics };
    }
    /**
     * 重置性能指标
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * // 执行一些操作...
     * map.resetMetrics(); // 重置所有计数器
     * ```
     */
    resetMetrics() {
        Object.keys(this.metrics).forEach(key => {
            this.metrics[key] = 0;
        });
    }
}
//# sourceMappingURL=concurrent.js.map