import { Socket as OriginalServerSocket } from 'socket.io';
import { Socket as OriginalClientSocket } from 'socket.io-client';
import { TextMessageWithState, User, Message, SystemMessage, ErrorMessage, RoomActiveStatus, UserLang, TranslateResult, ErrSource, Room, EChatModeType, EServerAckType } from './index.js';
export declare const WebSocketEvents: {
    readonly STANDARD: {
        readonly SERVER: {
            readonly CONNECTION: "connection";
            readonly DISCONNECTING: "disconnecting";
            readonly DISCONNECT: "disconnect";
        };
        readonly CLIENT: {
            readonly CONNECT: "connect";
            readonly DISCONNECT: "disconnect";
            readonly CONNECT_ERROR: "connect_error";
            readonly RECONNECT_ATTEMPT: "reconnect_attempt";
            readonly RECONNECTING: "reconnecting";
            readonly RECONNECT_ERROR: "reconnect_error";
            readonly RECONNECT_FAILED: "reconnect_failed";
            readonly RECONNECT: "reconnect";
            readonly PING: "ping";
            readonly PONG: "pong";
        };
    };
    readonly ROOM: {
        readonly UPDATE: "room:update";
        readonly JOIN: "room:join";
        readonly JOINED: "room:joined";
        readonly LEAVE: "room:leave";
        readonly LEFT: "room:leaved";
        readonly REENTER: "room:reenter";
        readonly RANDOM: {
            readonly MATCH: "room:random:match";
            readonly ACK: "room:random:ack";
        };
    };
    readonly MESSAGE: {
        readonly SEND: {
            readonly SENDING: "message:send";
            readonly DELIVERED: "message:delivered";
            readonly ACK: "message:ack";
        };
        readonly HISTORY: {
            readonly SHARE: "message:history:share";
            readonly OFFER: "message:history:offer";
        };
        readonly TRANSLATE: "message:translate";
    };
    readonly USER: {
        readonly KICK: "user:kick";
        readonly KICKED: "user:kicked";
    };
    readonly WARNNING: {
        readonly ROOM: {
            readonly IDLE: "warnning:room:idle";
        };
    };
    readonly ACTION: {
        readonly SERVER: {
            readonly FORCEDISCONNECT: "action:server:forceDisconnect";
            readonly INVITE: "action:server:invite";
        };
    };
    readonly ERROR: {
        readonly ROOM: {
            readonly JOIN: "error:room:join";
            readonly FULL: "error:room:full";
            readonly LEAVE: "error:room:leave";
            readonly NOT_FOUND: "error:room:not_found";
            readonly kICK: "error:room:kick";
        };
        readonly MESSAGE: {
            readonly SEND_FAILED: "error:message:send_failed";
            readonly DELIVERED_FAILED: "error:message:delivered_failed";
        };
        readonly USER: {
            readonly CONNECT: "error:user:connect";
            readonly NOT_EXISTS: "error:user:not_exists";
        };
        readonly UNKNOWN: "error:unknown";
        readonly SERVER: {
            readonly STATUS: "error:server:status";
            readonly MATCH_RANDOM: "error:server:match_random";
        };
    };
    readonly SYSTEM: "system";
    readonly SETTINGS: "setting";
    readonly SERVER: {
        readonly STATUS: "server:status";
    };
};
/**
 * 用户状态
 */
export declare enum UserStatus {
    ONLINE = "online",
    OFFLINE = "offline"
}
/**
 * 基础负载接口
 */
export interface BasePayload {
    timestamp: number;
}
export interface AckPayload extends BasePayload {
    ack: EServerAckType;
}
export interface BaseUserPayload extends BasePayload {
    userId: string;
    userName?: string;
}
/**
 * 基础房间负载接口
 */
export interface BaseRoomPayload extends BasePayload {
    roomId: number;
}
/**
 * 基础用户房间负载接口
 */
export interface BaseUserRoomPayload extends BaseRoomPayload {
    user: User;
}
/**
 * 加入房间请求负载
 */
export interface JoinRoomPayload extends BaseUserRoomPayload {
    userToken: string;
    chatMode: EChatModeType;
    reenter?: boolean;
    isInvitor?: boolean;
}
/**
 * 离开房间请求负载
 */
export interface LeaveRoomPayload extends BaseUserRoomPayload {
    isClient?: boolean;
    isServerLeave?: boolean;
    isForceLeave?: boolean;
    requesterId?: string;
}
export interface InvitePayload extends BaseUserPayload {
    invitorLang?: UserLang;
    retry?: number;
}
export interface KickUserPayload extends BaseUserRoomPayload {
    kickee: string[];
    userToken: string;
}
export interface UserKickedPayload extends BaseUserRoomPayload {
    isKicked: boolean;
    kickee: string;
}
/**
 * 房间更新响应负载
 */
export interface RoomUpdatedPayload extends BaseRoomPayload {
    users: User[];
    status: RoomActiveStatus;
    createdAt?: number;
    adminId?: string;
    lastActiveAt?: number;
    joinedUserId?: string;
    leavedUserId?: string;
    kickedUserIds?: string[];
}
export interface UserLeftPayload extends BaseRoomPayload {
    leftUserId?: string;
    isLeft?: boolean;
}
export interface UserJoinedPayload extends BasePayload {
    room: Room;
    joinedUserId: string;
    isJoin: boolean;
    userToken: string;
}
/**
 * 发送消息请求负载
 */
export interface SendMessagePayload extends BaseRoomPayload {
    userId: string;
    content: string;
    lang: UserLang;
    messageId?: string;
    fstSendTimestamp?: number;
}
/**
 * server status for REP to client
 */
export interface ServerStatusPayload extends BaseUserPayload, AckPayload {
    population: number;
    randomModeCount: number;
}
export interface ServerMatchRandomPayload extends BaseRoomPayload, AckPayload {
    isInvitor: boolean;
    userToken?: string;
}
export interface DeliveredMessageAckPayload extends BaseRoomPayload {
}
/**
 * 新消息响应负载
 */
export interface MessageDeliveredPayload extends BaseUserRoomPayload {
    messageWithState: TextMessageWithState;
}
/**
 * 消息历史负载
 */
export interface HistorySharePayload extends BaseRoomPayload {
    fromUserId: string;
    targetUserId: string;
    histories: Message[];
}
/**
 * 消息翻译负载
 */
export interface TranslatePayload extends BaseRoomPayload {
    sender: User;
    receiver: User;
    record: TranslateResult;
}
export interface ConfirmPayload extends BasePayload {
    type: 'confirm';
    messageId: string;
}
/**
 * 错误响应负载
 */
export interface ErrorPayload extends BasePayload {
    errMsg: ErrorMessage;
    source?: typeof ErrSource[keyof typeof ErrSource];
    eventType?: keyof ServerEmitEvents | keyof ClientEmitEvents;
    method?: string;
    moduleName?: string;
    roomId?: number;
    userId?: string;
    details?: Record<string, unknown>;
}
/**
 * 系统消息响应负载
 */
export interface SystemPayload extends BaseRoomPayload {
    sysMsg: SystemMessage;
    details?: Record<string, unknown>;
}
/**
 * client settings
 */
export interface ClientPayload extends BaseUserPayload {
    chatMode: EChatModeType;
    userToken?: string;
    details?: Record<string, unknown>;
}
export interface EmptyPayload {
}
/**
 * WebSocket事件类型统一定义
 */
export type UnibabbleWebSocketEventMap = {
    [WebSocketEvents.ROOM.JOIN]: {
        req: JoinRoomPayload;
        res: void;
    };
    [WebSocketEvents.ROOM.LEAVE]: {
        req: LeaveRoomPayload;
        res: void;
    };
    [WebSocketEvents.ROOM.REENTER]: {
        req: JoinRoomPayload;
        res: void;
    };
    [WebSocketEvents.MESSAGE.SEND.SENDING]: {
        req: SendMessagePayload;
        res: void;
    };
    [WebSocketEvents.MESSAGE.SEND.ACK]: {
        req: DeliveredMessageAckPayload;
        res: void;
    };
    [WebSocketEvents.MESSAGE.TRANSLATE]: {
        req: TranslatePayload;
        res: void;
    };
    [WebSocketEvents.SETTINGS]: {
        req: ClientPayload;
        res: AckPayload;
    };
    [WebSocketEvents.SERVER.STATUS]: {
        req: ClientPayload;
        res: ServerStatusPayload;
    };
    [WebSocketEvents.USER.KICK]: {
        req: KickUserPayload;
        res: void;
    };
    [WebSocketEvents.ROOM.RANDOM.MATCH]: {
        req: ClientPayload;
        res: void;
    };
    [WebSocketEvents.STANDARD.CLIENT.RECONNECTING]: {
        req: void;
        res: number;
    };
    [WebSocketEvents.STANDARD.CLIENT.RECONNECT_FAILED]: {
        req: void;
        res: EmptyPayload;
    };
    [WebSocketEvents.STANDARD.CLIENT.RECONNECT_ERROR]: {
        req: void;
        res: Error;
    };
    [WebSocketEvents.STANDARD.CLIENT.RECONNECT]: {
        req: void;
        res: EmptyPayload;
    };
    [WebSocketEvents.STANDARD.CLIENT.CONNECT_ERROR]: {
        req: void;
        res: Error;
    };
    [WebSocketEvents.STANDARD.CLIENT.PING]: {
        req: void;
        res: EmptyPayload;
    };
    [WebSocketEvents.STANDARD.CLIENT.PONG]: {
        req: void;
        res: number;
    };
    [WebSocketEvents.ROOM.UPDATE]: {
        req: void;
        res: RoomUpdatedPayload;
    };
    [WebSocketEvents.ROOM.JOINED]: {
        req: void;
        res: UserJoinedPayload;
    };
    [WebSocketEvents.ROOM.LEFT]: {
        req: void;
        res: UserLeftPayload;
    };
    [WebSocketEvents.MESSAGE.SEND.DELIVERED]: {
        req: void;
        res: MessageDeliveredPayload;
    };
    [WebSocketEvents.MESSAGE.HISTORY.SHARE]: {
        req: HistorySharePayload;
        res: void;
    };
    [WebSocketEvents.MESSAGE.HISTORY.OFFER]: {
        req: void;
        res: HistorySharePayload;
    };
    [WebSocketEvents.SYSTEM]: {
        req: void;
        res: SystemPayload;
    };
    [WebSocketEvents.USER.KICKED]: {
        req: void;
        res: UserKickedPayload;
    };
    [WebSocketEvents.ACTION.SERVER.INVITE]: {
        req: AckPayload;
        res: InvitePayload;
    };
    [WebSocketEvents.ROOM.RANDOM.ACK]: {
        req: void;
        res: ServerMatchRandomPayload;
    };
    [WebSocketEvents.ERROR.ROOM.FULL]: {
        req: void;
        res: ErrorPayload;
    };
    [WebSocketEvents.ERROR.ROOM.JOIN]: {
        req: void;
        res: ErrorPayload;
    };
    [WebSocketEvents.ERROR.ROOM.LEAVE]: {
        req: void;
        res: ErrorPayload;
    };
    [WebSocketEvents.ERROR.MESSAGE.SEND_FAILED]: {
        req: void;
        res: ErrorPayload;
    };
    [WebSocketEvents.ERROR.USER.CONNECT]: {
        req: void;
        res: ErrorPayload;
    };
    [WebSocketEvents.ERROR.UNKNOWN]: {
        req: void;
        res: ErrorPayload;
    };
    [WebSocketEvents.ERROR.ROOM.NOT_FOUND]: {
        req: void;
        res: ErrorPayload;
    };
    [WebSocketEvents.ERROR.USER.NOT_EXISTS]: {
        req: void;
        res: ErrorPayload;
    };
    [WebSocketEvents.ERROR.SERVER.STATUS]: {
        req: void;
        res: ErrorPayload;
    };
    [WebSocketEvents.ERROR.SERVER.MATCH_RANDOM]: {
        req: void;
        res: ErrorPayload;
    };
    [WebSocketEvents.WARNNING.ROOM.IDLE]: {
        req: void;
        res: SystemPayload;
    };
    [WebSocketEvents.ACTION.SERVER.FORCEDISCONNECT]: {
        req: void;
        res: SystemPayload;
    };
};
export type ServerEmitEvents = {
    [E in keyof UnibabbleWebSocketEventMap as UnibabbleWebSocketEventMap[E]['req'] extends void ? E : never]: (payload: UnibabbleWebSocketEventMap[E]['res']) => void;
} & {
    [E in keyof UnibabbleWebSocketEventMap as UnibabbleWebSocketEventMap[E]['req'] extends AckPayload ? E : never]: (payload: UnibabbleWebSocketEventMap[E]['res'], callback: (ack: AckPayload) => void) => void;
};
type ServerListenEvents = {
    [E in keyof UnibabbleWebSocketEventMap as UnibabbleWebSocketEventMap[E]['res'] extends void ? E : never]: (payload: UnibabbleWebSocketEventMap[E]['req']) => void;
} & {
    [E in keyof UnibabbleWebSocketEventMap as UnibabbleWebSocketEventMap[E]['res'] extends AckPayload ? E : never]: (payload: UnibabbleWebSocketEventMap[E]['req'], callback: (ack: AckPayload) => void) => void;
};
export interface ServerSocketData {
    roomId?: number;
    userId: string;
    userName?: string;
    email?: string;
    userToken?: string;
    lang?: number;
}
interface ServerSocketEvents {
    join: (room: string) => void;
    leave: (room: string) => void;
    to: (room: string) => ServerSocket;
}
export interface ServerSocket extends OriginalServerSocket<ServerListenEvents, ServerEmitEvents, ServerSocketEvents, ServerSocketData> {
}
export type ClientListenEvents = {
    [E in keyof UnibabbleWebSocketEventMap as UnibabbleWebSocketEventMap[E]['req'] extends void ? E : never]: (payload: UnibabbleWebSocketEventMap[E]['res']) => void;
} & {
    [E in keyof UnibabbleWebSocketEventMap as UnibabbleWebSocketEventMap[E]['req'] extends AckPayload ? E : never]: (payload: UnibabbleWebSocketEventMap[E]['res'], callback: (ack: AckPayload) => void) => void;
};
export type ClientEmitEvents = {
    [E in keyof UnibabbleWebSocketEventMap]: UnibabbleWebSocketEventMap[E]['res'] extends void ? (payload: UnibabbleWebSocketEventMap[E]['req']) => void : (payload: UnibabbleWebSocketEventMap[E]['req'], callback: (ack: UnibabbleWebSocketEventMap[E]['res']) => void) => void;
};
export interface ClientSocket extends OriginalClientSocket<ClientListenEvents, ClientEmitEvents> {
}
export {};
//# sourceMappingURL=websocket.d.ts.map