export var UserRole;
(function (UserRole) {
    UserRole[UserRole["MEMBER"] = 0] = "MEMBER";
    UserRole[UserRole["ADMIN"] = 1] = "ADMIN";
})(UserRole || (UserRole = {}));
export var RoomActiveStatus;
(function (RoomActiveStatus) {
    RoomActiveStatus[RoomActiveStatus["ACTIVE"] = 0] = "ACTIVE";
    RoomActiveStatus[RoomActiveStatus["INACTIVE"] = 1] = "INACTIVE";
    RoomActiveStatus[RoomActiveStatus["IDLE"] = 2] = "IDLE";
    RoomActiveStatus[RoomActiveStatus["BLOCKED"] = 3] = "BLOCKED";
})(RoomActiveStatus || (RoomActiveStatus = {}));
export var EMessageType;
(function (EMessageType) {
    EMessageType[EMessageType["TEXT"] = 0] = "TEXT";
    EMessageType[EMessageType["SYSTEM"] = 1] = "SYSTEM";
    EMessageType[EMessageType["ERROR"] = 2] = "ERROR";
})(EMessageType || (EMessageType = {}));
export var EChatModeType;
(function (EChatModeType) {
    EChatModeType[EChatModeType["NORMAL"] = 0] = "NORMAL";
    EChatModeType[EChatModeType["RANDOM"] = 1] = "RANDOM";
})(EChatModeType || (EChatModeType = {}));
export var EServerAckType;
(function (EServerAckType) {
    EServerAckType[EServerAckType["CONFIRM"] = 0] = "CONFIRM";
    EServerAckType[EServerAckType["REJECT"] = 1] = "REJECT";
})(EServerAckType || (EServerAckType = {}));
/**
 * 消息状态
 */
export var EMessageState;
(function (EMessageState) {
    EMessageState[EMessageState["SENDING"] = 0] = "SENDING";
    EMessageState[EMessageState["DELIVERED"] = 1] = "DELIVERED";
    EMessageState[EMessageState["ACK"] = 2] = "ACK";
    EMessageState[EMessageState["RESENDING"] = 3] = "RESENDING";
    EMessageState[EMessageState["FAILED"] = 4] = "FAILED";
})(EMessageState || (EMessageState = {}));
export const deeplLangs = [
    { "code": "BG", "target": "BG", "name": "Bulgarian", "originalLangName": "Bulgarian", "supports_formality": false },
    { "code": "CS", "target": "CS", "name": "Czech", "originalLangName": "Czech", "supports_formality": false },
    { "code": "DA", "target": "DA", "name": "Danish", "originalLangName": "Danish", "supports_formality": false },
    { "code": "DE", "target": "DE", "name": "German", "originalLangName": "Deutsch", "supports_formality": true },
    { "code": "EL", "target": "EL", "name": "Greek", "originalLangName": "Greek", "supports_formality": false },
    { "code": "EN", "target": "EN-GB", "name": "English (British)", "originalLangName": "English (British)", "supports_formality": false },
    { "code": "EN", "target": "EN-US", "name": "English (American)", "originalLangName": "English (American)", "supports_formality": false },
    { "code": "ES", "target": "ES", "name": "Spanish", "originalLangName": "Español", "supports_formality": true },
    { "code": "ET", "target": "ET", "name": "Estonian", "originalLangName": "Estonian", "supports_formality": false },
    { "code": "FI", "target": "FI", "name": "Finnish", "originalLangName": "Finnish", "supports_formality": false },
    { "code": "FR", "target": "FR", "name": "French", "originalLangName": "Français", "supports_formality": true },
    { "code": "HU", "target": "HU", "name": "Hungarian", "originalLangName": "Hungarian", "supports_formality": false },
    { "code": "ID", "target": "ID", "name": "Indonesian", "originalLangName": "Indonesian", "supports_formality": false },
    { "code": "IT", "target": "IT", "name": "Italian", "originalLangName": "Italiano", "supports_formality": true },
    { "code": "JA", "target": "JA", "name": "Japanese", "originalLangName": "日本語", "supports_formality": true },
    { "code": "KO", "target": "KO", "name": "Korean", "originalLangName": "Korean", "supports_formality": false },
    { "code": "LT", "target": "LT", "name": "Lithuanian", "originalLangName": "Lithuanian", "supports_formality": false },
    { "code": "LV", "target": "LV", "name": "Latvian", "originalLangName": "Latvian", "supports_formality": false },
    { "code": "NB", "target": "NB", "name": "Norwegian", "originalLangName": "Norwegian", "supports_formality": false },
    { "code": "NL", "target": "NL", "name": "Dutch", "originalLangName": "Nederlands", "supports_formality": true },
    { "code": "PL", "target": "PL", "name": "Polish", "originalLangName": "Polski", "supports_formality": true },
    { "code": "PT", "target": "PT-BR", "name": "Portuguese (Brazilian)", "originalLangName": "Português (Brasileiro)", "supports_formality": true },
    { "code": "PT", "target": "PT-PT", "name": "Portuguese (European)", "originalLangName": "Português (Europeu)", "supports_formality": true },
    { "code": "RO", "target": "RO", "name": "Romanian", "originalLangName": "Romanian", "supports_formality": false },
    { "code": "RU", "target": "RU", "name": "Russian", "originalLangName": "Русский", "supports_formality": true },
    { "code": "SK", "target": "SK", "name": "Slovak", "originalLangName": "Slovak", "supports_formality": false },
    { "code": "SL", "target": "SL", "name": "Slovenian", "originalLangName": "Slovenian", "supports_formality": false },
    { "code": "SV", "target": "SV", "name": "Swedish", "originalLangName": "Swedish", "supports_formality": false },
    { "code": "TR", "target": "TR", "name": "Turkish", "originalLangName": "Turkish", "supports_formality": false },
    { "code": "UK", "target": "UK", "name": "Ukrainian", "originalLangName": "Ukrainian", "supports_formality": false },
    { "code": "ZH", "target": "ZH-HANS", "name": "Chinese (simplified)", "originalLangName": "中文" }
];
export const AvailableLanguages = deeplLangs.reduce((map, lang) => {
    map.set(lang.target, lang.originalLangName);
    return map;
}, new Map());
//# sourceMappingURL=base.js.map