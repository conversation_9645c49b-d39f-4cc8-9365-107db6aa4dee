import type { LogLevel } from './logger.js';
export type EnvType = 'dev' | 'remote' | 'prod';
export interface BaseServiceConfig {
    /** 服务器主机 */
    host: string;
    /** 服务器端口 */
    port: number;
    /** WebSocket路径 */
    ws: string;
    /** API路径前缀 */
    api: string;
}
export interface LogConfig {
    /** 日志级别 */
    level: LogLevel;
}
export interface OAuthConfig {
    enable: boolean;
    google: {
        clientId: string;
    };
}
export interface ChatConfig {
    perInviteTimeout: number;
    randomInviteCount: number;
}
export interface CommonConfig {
    env: EnvType;
    auth: OAuthConfig;
    log: LogConfig;
    server: BaseServiceConfig;
    chat: ChatConfig;
}
/*******************************/
/************server*************/
/*******************************/
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'OPTIONS';
export interface CorsConfig {
    /** 允许的源 */
    origin: string | string[];
    /** 允许的HTTP方法 */
    methods: HttpMethod[];
    /** 允许的请求头 */
    allowedHeaders: string[];
}
export interface ServerConnectionConfig {
    /** 心跳间隔（毫秒） */
    pingInterval: number;
    /** 心跳超时（毫秒） */
    pingTimeout: number;
}
export interface MessageConfig {
    /** 最大消息长度（字符数） */
    messageMaxLength: number;
    /** 消息发送速率限制（条/分钟） */
    messageRateLimit: number;
    /** 消息发送间隔, ms */
    messageSendInterval: number;
    /** 消息处理重试次数 */
    messageRetryAttempts: number;
    /** 消息重试延迟（毫秒） */
    messageRetryDelay: number;
}
export interface RoomConfig {
    /** 每个房间的最大历史消息数量 */
    roomMaxHistorySize: number;
    /** 每个房间的最大用户数量 */
    roomMaxUsers: number;
    /** 房间最大闲置时间, ms */
    roomMaxIdle: number;
    /** 空闲房间扫描间隔, ms */
    roomMaxIdleCheckInterval: number;
    /** 强制离线间隔, ms */
    roomForceDisconnectInterval: number;
    /** 用户状态检查间隔, ms */
    roomUserStatusCheckInterval: number;
    roomMaxCount: number;
}
export interface LLMApiConfig {
    enable: boolean;
    provider: string;
    baseUrl: string;
    headers: Record<string, string>;
    apiKey: string;
    model: string;
    timeout?: number;
    maxTokens?: number;
    temperature?: number;
}
export interface DeepLApiConfig {
    enable: boolean;
    provider: string;
    baseUrl: string;
    headers: Record<string, string>;
    apiKey: string;
    model: string;
}
export interface ServerConfig extends CommonConfig {
    cors: Partial<CorsConfig>;
    connection: ServerConnectionConfig;
    message: MessageConfig;
    room: RoomConfig;
    translation: {
        mistral: LLMApiConfig;
        deepl: DeepLApiConfig;
    };
}
/*******************************/
/************client*************/
/*******************************/
export interface ClientConnectionConfig {
    /** 重连基础延迟（毫秒） */
    reconnectionDelay: number;
    /** 最大重连延迟（毫秒） */
    reconnectionDelayMax: number;
    /** 重连尝试次数 */
    reconnectionAttempts: number;
    /** 连接超时（毫秒） */
    connectTimeout: number;
    /** 等待确认超时（毫秒） */
    ackTimeout?: number;
}
export interface AppConfig {
    /** 应用标题 */
    title: string;
    /** 应用版本 */
    version: string;
    /** 应用描述 */
    description: string;
    /** 应用URL */
    url: string;
}
export interface ClientServiceConfig {
    host: string;
    port: number;
    secureProtocol: boolean;
}
export interface ClientConfig extends CommonConfig {
    client: ClientServiceConfig;
    /** client 连接配置 */
    connection: ClientConnectionConfig;
    /** 应用配置 */
    app: AppConfig;
}
//# sourceMappingURL=config.d.ts.map