import { AxiosResponse } from 'axios';
export declare enum HttpStatusCode {
    OK = 200,
    CREATED = 201,
    NO_CONTENT = 204,
    BAD_REQUEST = 400,
    UNAUTHORIZED = 401,
    FORBIDDEN = 403,
    NOT_FOUND = 404,
    INTERNAL_SERVER_ERROR = 500
}
export interface ApiResponse<T = any, D = any> extends Partial<AxiosResponse<T, D>> {
    code: ErrorCode;
    message?: string;
}
/**
 * 错误码枚举
 */
export declare enum ErrorCode {
    /** WEBSOCKET 连接错误 */
    CONNECT_CLIENT_ERROR = "connect_client_error",// 统一连接错误
    CONNECT_ERROR = "connect_error",// 连接错误
    CONNECT_TIMEOUT = "connect_timeout",// 连接超时
    RECONNECT_FAILED = "reconnect_failed",// 重连失败
    RECONNECT_ERROR = "reconnect_error",// 重连错误
    SERVER_FORCE_DISCONNECT = "server_force_disconnect",// 服务器强制断开连接
    CLIENT_RECONNECTING = "client_reconnecting",
    CLIENT_RECONNECT_ERROR = "client_reconnect_error",
    SERVER_PING_TIMEOUT = "ping timeout",// server ping client 客户端没有在规定的时间内发送心跳包（pong），导致连接超时。
    SERVER_TRANSPORT_CLOSE = "transport close",// 底层传输层（例如 WebSocket）连接关闭。
    SERVER_PARSE_ERROR = "parse error",// 客户端发送的数据无法被服务端解析. 一般是由于自定义的包解析器出现问题导致的.
    SERVER_TRANSPORT_ERROR = "transport error",// 服务器可能因为安全原因、资源限制、维护等原因主动关闭某个客户端的连接。
    SERVER_IO_SERVER_DISCONNECT = "io server disconnect",// 服务器主动调用 socket.disconnect()。这可能是由于网络问题、协议错误、服务器错误等原因导致的。具体的错误信息通常会包含在错误对象中（如果有的话）。
    SERVER_IO_CLIENT_DISCONNECT = "io client disconnect",// 客户端主动调用 socket.disconnect()。用户可能点击了“退出登录”按钮，或者客户端应用程序主动关闭了连接。
    CLIENT_PING_TIMEOUT = "ping timeout",// client pong server 服务器没有在规定的时间内发送心跳包（ping），导致连接超时。
    CLIENT_TRANSPORT_CLOSE = "transport close",// 底层传输层（例如 WebSocket）连接关闭。
    CLIENT_PARSE_ERROR = "parse error",// 客户端收到了一个无法解析的包。 一般是由于自定义的包解析器出现问题导致的.
    CLIENT_TRANSPORT_ERROR = "transport error",// 客户端可能因为安全原因、资源限制、维护等原因主动关闭某个连接。
    CLIENT_IO_SERVER_DISCONNECT = "io server disconnect",// 服务器主动调用 socket.disconnect()。这可能是由于网络问题、协议错误、服务器错误等原因导致的。具体的错误信息通常会包含在错误对象中（如果有的话）。
    CLIENT_IO_CLIENT_DISCONNECT = "io client disconnect",// 客户端主动调用 socket.disconnect()。用户可能点击了“退出登录”按钮，或者客户端应用程序主动关闭了连接。
    /** 房间错误 */
    ROOM_NOT_EXISTS = "room_not_exists",// 房间不存在
    ROOM_NOT_FOUND = "room_not_found",// 房间不存在
    ROOM_NOT_MATCH = "room_not_match",// 房间不匹配
    ROOM_NOT_ACTIVE = "room_not_active",// 房间不活跃
    ROOM_FULL = "room_full",// 房间已满
    ROOM_CLOSED = "room_closed",// 房间已关闭
    ROOM_JOIN_FAILED = "join_room_failed",// 加入房间失败
    ROOM_JOIN_TIMEOUT = "join_room_timeout",// 加入房间超时
    ROOM_LEAVE_FAILED = "leave_room_failed",// 离开房间失败
    ROOM_CHECK_ID_FAILED = "check_room_id_failed",// 检查房间ID失败
    ROOM_REENTER_FAILED = "reenter_room_failed",// 重新进入房间失败
    ROOM_ADMIN_CANNOT_LEAVE = "admin_cannot_leave",// 管理员不能离开房间
    ROOM_ADMIN_ONLY = "room_admin_only",// 管理员只能离开房间
    ROOM_INVALID_PARAMS = "room_invalid_params",
    ROOM_MATCH_RANDOM_FAILED = "room_match_random_failed",
    /** 消息错误 */
    MESSAGE_SEND_FAILED = "message_send_failed",// 发送失败
    MESSAGE_TOO_LONG = "message_too_long",// 消息过长
    MESSAGE_CONTENT_NULL = "message_content_null",// 消息内容为空
    TRANSLATION_FAILED = "translation_failed",// 翻译失败
    MESSAGE_SEND_RATE_LIMIT_EXCEEDED = "message_send_rate_limit_exceeded",// 消息发送速率限制超出
    INVALID_MESSAGE_CONTENT = "invalid_message_content",// 无效的消息内容
    /** 用户错误 */
    USER_NOT_IN_ROOM = "user_not_in_room",// 用户不在房间
    USER_BANNED = "user_banned",// 用户被禁止
    USER_ALREADY_IN_ROOM = "user_already_in_room",// 用户已在房间
    USER_NOT_EXISTS = "user_not_exists",// 用户已在房间
    USER_CONNECT_ERROR = "user_connect_error",
    USER_KICK_ERROR = "user_kick_error",
    /** HTTP */
    HTTP_GET_ERROR = "http_get_error",// API 响应错误
    HTTP_POST_ERROR = "http_post_error",// API 响应错误
    HTTP_ERROR = "http_error",// API 响应错误
    INVALID_API_RESPONSE = "invalid_api_response",// 无效的 API 响应
    API_RESPONSE_ERROR = "api_response_error",// API 响应错误
    API_DEEPL_RESPONSE_NO_CONTENT = "api_deepl_response_no_content",// API Deepl 响应无内容
    API_RESPONSE_NO_DATA = "api_response_no_data",// API 响应无数据
    API_DEEPL_ERROR = "api_deepl_error",// API Deepl 错误
    API_DEEPL_KEY_NOT_CONFIGURED = "api_deepl_key_not_configured",// API Deepl Key 未配置
    API_DEEPL_TARGET_LANGS_NO_RESPONSE = "api_deepl_target_langs_no_response",// API Deepl 目标语言获取无响应
    LOGIN_GOOGLE_AUTH_FAILED = "login_google_auth_failed",
    AUTH_GOOGLE_TOKEN_NOT_SET = "auth_google_token_not_set",
    AUTH_GOOGLE_TIMEOUT = "auth_google_timeout",
    /** 权限错误 */
    UNAUTHORIZED = "unauthorized",
    FORBIDDEN = "forbidden",
    WORKERPOOL_NOT_INITIALIZED = "workerpool_not_initialized",
    /** 未知错误 */
    UNKNOWN_ERROR = "unknown_error",// 未知错误
    OK = "ok"
}
//# sourceMappingURL=http.d.ts.map