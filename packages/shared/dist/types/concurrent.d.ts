/**
 * 高性能并发 Map 实现
 * 采用分段锁策略，优化并发性能
 *
 * 使用示例:
 * ```typescript
 * // 创建实例
 * const map = new ConcurrentMap<string, number>({
 *   segmentCount: 64,  // 更多的分段以支持更高并发
 *   lockTimeout: 500,  // 更短的超时时间
 *   maxRetries: 5      // 更多的重试次数
 * });
 *
 * // 基本操作
 * await map.set('key1', 100);
 * const value = await map.get('key1');
 *
 * // 原子更新
 * await map.update('key1', (current = 0) => current + 1);
 *
 * // 批量更新
 * const updates = new Map([
 *   ['key1', (v = 0) => v + 1],
 *   ['key2', (v = 0) => v + 2]
 * ]);
 * await map.batchUpdate(updates);
 *
 * // 监控性能
 * console.log(map.getMetrics());
 *
 * 使用场景:
 * 1. 小数据量，低并发：使用原生 Map
 * const simpleMap = new Map<string, number>();
 *
 * 2. 中等数据量，需要并发：使用较少分段
 * const mediumMap = new ConcurrentMap<string, number>({
 *   segmentCount: 16
 * });
 *
 * 3. 大数据量，高并发：使用默认配置或更多分段
 * const largeMap = new ConcurrentMap<string, number>({
 *   segmentCount: 64
 * });
 *
 * ```
 *
 * @template K 键的类型
 * @template V 值的类型
 */
export declare class ConcurrentMap<K, V> {
    private readonly segments;
    private readonly config;
    private readonly metrics;
    constructor(options?: {
        segmentCount?: number;
        lockTimeout?: number;
        maxRetries?: number;
        backoffBase?: number;
    });
    /**
     * 获取键对应的分段索引
     * 使用改进的哈希算法确保均匀分布
     */
    private getSegmentIndex;
    /**
     * 获取指定分段的锁
     * 使用指数退避策略
     */
    private acquireSegmentLock;
    /**
     * 释放分段锁
     */
    private releaseSegmentLock;
    /**
     * 执行需要加锁的操作
     */
    private withSegmentLock;
    /**
     * 设置键值对
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * await map.set('counter', 1);
     * ```
     */
    set(key: K, value: V): Promise<this>;
    /**
     * 获取值
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * await map.set('counter', 1);
     * const value = await map.get('counter'); // 1
     * ```
     */
    get(key: K): Promise<V | undefined>;
    /**
     * 原子更新操作
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * // 原子递增
     * await map.update('counter', (current = 0) => current + 1);
     * ```
     */
    update(key: K, updateFn: (currentValue: V | undefined) => V): Promise<V>;
    /**
     * 批量更新操作
     * 按分段分组并顺序处理，避免死锁
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * // 批量更新多个计数器
     * const updates = new Map([
     *   ['counter1', (v = 0) => v + 1],
     *   ['counter2', (v = 0) => v + 2]
     * ]);
     * await map.batchUpdate(updates);
     * ```
     */
    batchUpdate(updates: Map<K, (currentValue: V | undefined) => V>): Promise<Map<K, V>>;
    /**
     * 检查键是否存在
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * await map.set('key', 1);
     * const exists = await map.has('key'); // true
     * ```
     */
    has(key: K): Promise<boolean>;
    /**
     * 删除键值对
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * await map.set('temp', 1);
     * await map.delete('temp');
     * ```
     */
    delete(key: K): Promise<boolean>;
    /**
     * 获取当前大小
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * await map.set('key1', 1);
     * await map.set('key2', 2);
     * const size = await map.size(); // 2
     * ```
     */
    size(): Promise<number>;
    /**
     * 清空 Map
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * await map.set('key', 1);
     * await map.clear();
     * const size = await map.size(); // 0
     * ```
     */
    clear(): Promise<void>;
    /**
     * 获取性能指标
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * // 执行一些操作...
     * const metrics = map.getMetrics();
     * console.log(metrics.reads, metrics.writes);
     * ```
     */
    getMetrics(): Readonly<typeof this.metrics>;
    /**
     * 重置性能指标
     * @example
     * ```typescript
     * const map = new ConcurrentMap<string, number>();
     * // 执行一些操作...
     * map.resetMetrics(); // 重置所有计数器
     * ```
     */
    resetMetrics(): void;
}
//# sourceMappingURL=concurrent.d.ts.map