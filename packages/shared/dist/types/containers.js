/**
 *  const vector = new FixedLengthArray<number, 3>(3);
    vector.create(1); // 自动分配位置，添加到索引 0 处
    vector.create(2); // 自动分配位置，添加到索引 1 处
    vector.create(3); // 自动分配位置，添加到索引 2 处

    vector.create(0, 4); // 手动指定位置，添加到索引 0 处

    const x = vector.read(0); // 4

    const latencies = new FixedLengthArray<number>(1000);
    // 现在支持以下操作
    const maxLatency = Math.max(...latencies);
    const avgLatency = [...latencies].reduce((acc, cur) => acc + cur, 0) / latencies.len;
    const mappedLatencies = latencies.map(x => x * 2);
 */
export class FixedLengthArray {
    data;
    length;
    currentIndex = 0;
    constructor(length) {
        if (length <= 0 || length > 4294967295) {
            throw new Error(`Length must be greater than 0 and less than or equal to 4294967295`);
        }
        this.length = length;
        this.data = new Array(length);
    }
    // 实现迭代器接口
    *[Symbol.iterator]() {
        for (const item of this.data) {
            if (item !== undefined) {
                yield item;
            }
        }
    }
    // 数组方法实现
    map(callbackfn) {
        return this.data.map(callbackfn);
    }
    reduce(callbackfn, initialValue) {
        return this.data.reduce(callbackfn, initialValue);
    }
    filter(predicate) {
        return this.data.filter(predicate);
    }
    forEach(callbackfn) {
        this.data.forEach(callbackfn);
    }
    find(predicate) {
        return this.data.find(predicate);
    }
    push(item) {
        const index = this.currentIndex % this.length;
        this.data[index] = item;
        this.currentIndex++;
    }
    get(index) {
        return this.data[index];
    }
    set(index, item) {
        this.data[index] = item;
    }
    remove(index) {
        this.data[index] = undefined;
    }
    get len() {
        return this.data.filter(item => item !== undefined).length;
    }
    get lengthValue() {
        return this.length;
    }
    // 支持展开运算符
    toArray() {
        return [...this];
    }
}
//# sourceMappingURL=containers.js.map