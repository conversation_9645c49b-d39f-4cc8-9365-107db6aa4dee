/**
 *  const vector = new FixedLengthArray<number, 3>(3);
    vector.create(1); // 自动分配位置，添加到索引 0 处
    vector.create(2); // 自动分配位置，添加到索引 1 处
    vector.create(3); // 自动分配位置，添加到索引 2 处

    vector.create(0, 4); // 手动指定位置，添加到索引 0 处

    const x = vector.read(0); // 4

    const latencies = new FixedLengthArray<number>(1000);
    // 现在支持以下操作
    const maxLatency = Math.max(...latencies);
    const avgLatency = [...latencies].reduce((acc, cur) => acc + cur, 0) / latencies.len;
    const mappedLatencies = latencies.map(x => x * 2);
 */
export declare class FixedLengthArray<T> {
    private data;
    private length;
    private currentIndex;
    constructor(length: number);
    [Symbol.iterator](): Iterator<T>;
    map<U>(callbackfn: (value: T, index: number, array: T[]) => U): U[];
    reduce<U>(callbackfn: (previousValue: U, currentValue: T, currentIndex: number, array: T[]) => U, initialValue: U): U;
    filter(predicate: (value: T, index: number, array: T[]) => boolean): T[];
    forEach(callbackfn: (value: T, index: number, array: T[]) => void): void;
    find(predicate: (value: T, index: number, obj: T[]) => boolean): T | undefined;
    push(item: T): void;
    get(index: number): T;
    set(index: number, item: T): void;
    remove(index: number): void;
    get len(): number;
    get lengthValue(): number;
    toArray(): T[];
}
//# sourceMappingURL=containers.d.ts.map