export const SystemMessageTemplate = {
    JOINED: (userName) => `${userName} join this room`,
    LEFT: (userName) => `${userName} left this room`,
    KICKED: (userName) => `${userName} has been kicked`,
    BANNED: (userName) => `${userName} banned`,
    UNBANNED: (userName) => `${userName} unbanned`,
    MODERATED: (userName) => `${userName} moderated`,
    UNMODERATED: (userName) => `${userName} unmoderated`,
    MUTE: (userName) => `${userName} muted`,
    UNMUTE: (userName) => `${userName} unmuted`,
    BROADCAST: 'broadcast',
};
//# sourceMappingURL=messages.js.map