{"version": 3, "file": "concurrent.js", "sourceRoot": "", "sources": ["../../src/types/concurrent.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgDG;AACH,MAAM,OAAO,aAAa;IACxB,OAAO;IACU,QAAQ,CAGtB;IAEH,MAAM;IACW,MAAM,CAKrB;IAEF,OAAO;IACU,OAAO,GAAG;QACzB,KAAK,EAAE,CAAC;QACR,MAAM,EAAE,CAAC;QACT,SAAS,EAAE,CAAC;QACZ,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,CAAC;KACZ,CAAC;IAEF,YAAY,UAKR,EAAE;QACJ,IAAI,CAAC,MAAM,GAAG;YACZ,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,EAAE,EAAK,UAAU;YACvD,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI,EAAM,SAAS;YACvD,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,CAAC,EAAU,WAAW;YACxD,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE,CAAO,eAAe;SAC7D,CAAC;QAEF,QAAQ;QACR,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CACxB,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,EACpC,GAAG,EAAE,CAAC,CAAC;YACL,MAAM,EAAE,IAAI,GAAG,EAAQ;YACvB,IAAI,EAAE,IAAI,UAAU,CAAC,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC;SAC/C,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;;OAGG;IACK,eAAe,CAAC,GAAM;QAC5B,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QACxB,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;QACrB,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IACnD,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,kBAAkB,CAC9B,YAAoB,EACpB,UAAkB,CAAC;QAEnB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,IAAI,EAAE,CAAC;YACZ,QAAQ;YACR,IAAI,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,SAAS;YACT,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBACrD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACxB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,SAAS;YACT,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAC1B,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,EAC9C,GAAG,CAAE,YAAY;aAClB,CAAC;YAEF,QAAQ;YACR,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,YAAoB;QAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC5C,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAC3B,YAAoB,EACpB,SAA+B;QAE/B,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,OAAO,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACxC,IAAI,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE,CAAC;gBACzD,IAAI,CAAC;oBACH,OAAO,MAAM,SAAS,EAAE,CAAC;gBAC3B,CAAC;wBAAS,CAAC;oBACT,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;gBACxC,CAAC;YACH,CAAC;YACD,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,UAAU,YAAY,KAAK,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,GAAG,CAAC,GAAM,EAAE,KAAQ;QACxB,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC/C,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,GAAG,EAAE;YAC5C,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACnD,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,GAAG,CAAC,GAAM;QACd,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,GAAG,EAAE;YAC7C,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,MAAM,CACV,GAAM,EACN,QAA4C;QAE5C,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,GAAG,EAAE;YAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;YACxC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACtB,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,KAAK,CAAC,WAAW,CACf,OAAmD;QAEnD,WAAW;QACX,MAAM,aAAa,GAAG,IAAI,GAAG,EAA2C,CAAC;QAEzE,KAAK,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,OAAO,EAAE,CAAC;YACtC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAC/C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;gBACrC,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;YAC7C,CAAC;YACD,aAAa,CAAC,GAAG,CAAC,YAAY,CAAE,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACtD,CAAC;QAED,YAAY;QACZ,MAAM,OAAO,GAAG,IAAI,GAAG,EAAQ,CAAC;QAChC,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAE/D,KAAK,MAAM,YAAY,IAAI,cAAc,EAAE,CAAC;YAC1C,MAAM,cAAc,GAAG,aAAa,CAAC,GAAG,CAAC,YAAY,CAAE,CAAC;YACxD,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,GAAG,EAAE;gBAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gBAC5C,KAAK,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,cAAc,EAAE,CAAC;oBAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAC7C,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;oBACxC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;oBAClC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;oBAC3B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACxB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,GAAG,CAAC,GAAM;QACd,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,GAAG,EAAE;YAC7C,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,MAAM,CAAC,GAAM;QACjB,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,GAAG,EAAE;YAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC9D,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACxB,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,MAAM,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,GAAG,EAAE;gBACjC,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,KAAK;QACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,MAAM,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,GAAG,EAAE;gBACjC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBAChC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACxB,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,UAAU;QACR,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;;;;OAQG;IACH,YAAY;QACV,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACrC,IAAI,CAAC,OAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC;CACF"}