import { ClientEmitEvents, ServerEmitEvents, ErrorCode } from "./index.js";
export declare const ErrSource: {
    readonly CLIENT: "CLIENT";
    readonly SERVER: "SERVER";
    readonly DEFAULT: "DEFAULT";
};
export declare class UnibabbleError extends Error {
    source: typeof ErrSource[keyof typeof ErrSource];
    code: ErrorCode;
    message: string;
    method?: string | undefined;
    module?: string | undefined;
    roomId?: number | undefined;
    userId?: string | undefined;
    eventType?: (keyof ServerEmitEvents | keyof ClientEmitEvents) | undefined;
    details?: Record<string, unknown> | undefined;
    constructor(source: typeof ErrSource[keyof typeof ErrSource], code: ErrorCode, message: string, method?: string | undefined, module?: string | undefined, roomId?: number | undefined, userId?: string | undefined, eventType?: (keyof ServerEmitEvents | keyof ClientEmitEvents) | undefined, details?: Record<string, unknown> | undefined);
}
//# sourceMappingURL=error.d.ts.map