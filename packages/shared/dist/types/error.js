export const ErrSource = { CLIENT: 'CLIENT', SERVER: 'SERVER', DEFAULT: 'DEFAULT' };
export class UnibabbleError extends Error {
    source;
    code;
    message;
    method;
    module;
    roomId;
    userId;
    eventType;
    details;
    constructor(source, code, message, method, module, roomId, userId, eventType, details) {
        super(message);
        this.source = source;
        this.code = code;
        this.message = message;
        this.method = method;
        this.module = module;
        this.roomId = roomId;
        this.userId = userId;
        this.eventType = eventType;
        this.details = details;
        this.source = source;
        this.code = code;
        this.method = method;
        this.roomId = roomId;
        this.userId = userId;
        this.eventType = eventType;
        this.details = details;
        this.module = module;
    }
}
//# sourceMappingURL=error.js.map