export var HttpStatusCode;
(function (HttpStatusCode) {
    HttpStatusCode[HttpStatusCode["OK"] = 200] = "OK";
    HttpStatusCode[HttpStatusCode["CREATED"] = 201] = "CREATED";
    HttpStatusCode[HttpStatusCode["NO_CONTENT"] = 204] = "NO_CONTENT";
    HttpStatusCode[HttpStatusCode["BAD_REQUEST"] = 400] = "BAD_REQUEST";
    HttpStatusCode[HttpStatusCode["UNAUTHORIZED"] = 401] = "UNAUTHORIZED";
    HttpStatusCode[HttpStatusCode["FORBIDDEN"] = 403] = "FORBIDDEN";
    HttpStatusCode[HttpStatusCode["NOT_FOUND"] = 404] = "NOT_FOUND";
    HttpStatusCode[HttpStatusCode["INTERNAL_SERVER_ERROR"] = 500] = "INTERNAL_SERVER_ERROR";
})(HttpStatusCode || (HttpStatusCode = {}));
/**
 * 错误码枚举
 */
export var ErrorCode;
(function (ErrorCode) {
    /** WEBSOCKET 连接错误 */
    ErrorCode["CONNECT_CLIENT_ERROR"] = "connect_client_error";
    ErrorCode["CONNECT_ERROR"] = "connect_error";
    ErrorCode["CONNECT_TIMEOUT"] = "connect_timeout";
    ErrorCode["RECONNECT_FAILED"] = "reconnect_failed";
    ErrorCode["RECONNECT_ERROR"] = "reconnect_error";
    ErrorCode["SERVER_FORCE_DISCONNECT"] = "server_force_disconnect";
    ErrorCode["CLIENT_RECONNECTING"] = "client_reconnecting";
    ErrorCode["CLIENT_RECONNECT_ERROR"] = "client_reconnect_error";
    // server 系统disconnect reason
    ErrorCode["SERVER_PING_TIMEOUT"] = "ping timeout";
    ErrorCode["SERVER_TRANSPORT_CLOSE"] = "transport close";
    ErrorCode["SERVER_PARSE_ERROR"] = "parse error";
    ErrorCode["SERVER_TRANSPORT_ERROR"] = "transport error";
    ErrorCode["SERVER_IO_SERVER_DISCONNECT"] = "io server disconnect";
    ErrorCode["SERVER_IO_CLIENT_DISCONNECT"] = "io client disconnect";
    // client 系统disconnect reason
    ErrorCode["CLIENT_PING_TIMEOUT"] = "ping timeout";
    ErrorCode["CLIENT_TRANSPORT_CLOSE"] = "transport close";
    ErrorCode["CLIENT_PARSE_ERROR"] = "parse error";
    ErrorCode["CLIENT_TRANSPORT_ERROR"] = "transport error";
    ErrorCode["CLIENT_IO_SERVER_DISCONNECT"] = "io server disconnect";
    ErrorCode["CLIENT_IO_CLIENT_DISCONNECT"] = "io client disconnect";
    /** 房间错误 */
    ErrorCode["ROOM_NOT_EXISTS"] = "room_not_exists";
    ErrorCode["ROOM_NOT_FOUND"] = "room_not_found";
    ErrorCode["ROOM_NOT_MATCH"] = "room_not_match";
    ErrorCode["ROOM_NOT_ACTIVE"] = "room_not_active";
    ErrorCode["ROOM_FULL"] = "room_full";
    ErrorCode["ROOM_CLOSED"] = "room_closed";
    ErrorCode["ROOM_JOIN_FAILED"] = "join_room_failed";
    ErrorCode["ROOM_JOIN_TIMEOUT"] = "join_room_timeout";
    ErrorCode["ROOM_LEAVE_FAILED"] = "leave_room_failed";
    ErrorCode["ROOM_CHECK_ID_FAILED"] = "check_room_id_failed";
    ErrorCode["ROOM_REENTER_FAILED"] = "reenter_room_failed";
    ErrorCode["ROOM_ADMIN_CANNOT_LEAVE"] = "admin_cannot_leave";
    ErrorCode["ROOM_ADMIN_ONLY"] = "room_admin_only";
    ErrorCode["ROOM_INVALID_PARAMS"] = "room_invalid_params";
    ErrorCode["ROOM_MATCH_RANDOM_FAILED"] = "room_match_random_failed";
    /** 消息错误 */
    ErrorCode["MESSAGE_SEND_FAILED"] = "message_send_failed";
    ErrorCode["MESSAGE_TOO_LONG"] = "message_too_long";
    ErrorCode["MESSAGE_CONTENT_NULL"] = "message_content_null";
    ErrorCode["TRANSLATION_FAILED"] = "translation_failed";
    ErrorCode["MESSAGE_SEND_RATE_LIMIT_EXCEEDED"] = "message_send_rate_limit_exceeded";
    ErrorCode["INVALID_MESSAGE_CONTENT"] = "invalid_message_content";
    /** 用户错误 */
    ErrorCode["USER_NOT_IN_ROOM"] = "user_not_in_room";
    ErrorCode["USER_BANNED"] = "user_banned";
    ErrorCode["USER_ALREADY_IN_ROOM"] = "user_already_in_room";
    ErrorCode["USER_NOT_EXISTS"] = "user_not_exists";
    ErrorCode["USER_CONNECT_ERROR"] = "user_connect_error";
    ErrorCode["USER_KICK_ERROR"] = "user_kick_error";
    /** HTTP */
    ErrorCode["HTTP_GET_ERROR"] = "http_get_error";
    ErrorCode["HTTP_POST_ERROR"] = "http_post_error";
    ErrorCode["HTTP_ERROR"] = "http_error";
    // api
    ErrorCode["INVALID_API_RESPONSE"] = "invalid_api_response";
    ErrorCode["API_RESPONSE_ERROR"] = "api_response_error";
    ErrorCode["API_DEEPL_RESPONSE_NO_CONTENT"] = "api_deepl_response_no_content";
    ErrorCode["API_RESPONSE_NO_DATA"] = "api_response_no_data";
    ErrorCode["API_DEEPL_ERROR"] = "api_deepl_error";
    ErrorCode["API_DEEPL_KEY_NOT_CONFIGURED"] = "api_deepl_key_not_configured";
    ErrorCode["API_DEEPL_TARGET_LANGS_NO_RESPONSE"] = "api_deepl_target_langs_no_response";
    //auth
    ErrorCode["LOGIN_GOOGLE_AUTH_FAILED"] = "login_google_auth_failed";
    ErrorCode["AUTH_GOOGLE_TOKEN_NOT_SET"] = "auth_google_token_not_set";
    ErrorCode["AUTH_GOOGLE_TIMEOUT"] = "auth_google_timeout";
    /** 权限错误 */
    ErrorCode["UNAUTHORIZED"] = "unauthorized";
    ErrorCode["FORBIDDEN"] = "forbidden";
    // server
    ErrorCode["WORKERPOOL_NOT_INITIALIZED"] = "workerpool_not_initialized";
    /** 未知错误 */
    ErrorCode["UNKNOWN_ERROR"] = "unknown_error";
    ErrorCode["OK"] = "ok";
})(ErrorCode || (ErrorCode = {}));
//# sourceMappingURL=http.js.map