{
  "compilerOptions": {
    "tsBuildInfoFile": "./dist/.tsbuildinfo",
    // 编译目标版本
    "target": "ES2024",
    // 模块系统
    "module": "ESNext",
    // 模块解析策略
    "moduleResolution": "bundler",
    // 输出目录
    "outDir": "./dist",
    // 源码目录
    "rootDir": "./src",
    // 生成声明文件
    "declaration": true,
    // 生成声明映射文件
    "declarationMap": true,
    // 生成源映射文件
    "sourceMap": true,
    // 启用严格模式
    "strict": true,
    // 跳过库检查
    "skipLibCheck": true,
    // 启用项目引用
    "composite": true,
    // 确保导入的大小写正确
    "forceConsistentCasingInFileNames": true,
    // 不允许隐式的 any
    "noImplicitAny": true,
    // 确保函数有返回值
    "noImplicitReturns": true,
    // 不允许未使用的局部变量
    "noUnusedLocals": true,
    // 不允许未使用的参数
    "noUnusedParameters": true,
    // 启用 ES 模块互操作性
    "esModuleInterop": true,
    // 允许 JSON 导入
    "resolveJsonModule": true,
    // 确保每个文件是模块
    "isolatedModules": true,
    // 保留所有文件
    "removeComments": false,
    // 生成所有文件
    "emitDeclarationOnly": false
  },
  // 包含的文件
  "include": [
    "src/**/*.ts"
  ],
  // 排除的文件
  "exclude": [
    "node_modules",
    "dist",
    "**/*.spec.ts",
    "**/*.test.ts"
  ]
}