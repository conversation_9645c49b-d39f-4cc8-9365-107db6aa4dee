import { createErrorMessage, getTimestamp, logger } from "../utils/index.js";
import { ErrorPayload, ClientEmitEvents, ServerEmitEvents } from "../types/index.js";
import { ErrSource, UnibabbleError, ErrorCode } from "../types/index.js";

// 客户端错误转换
export const createCliErr = (
    code: ErrorCode,
    message: string,
    method?: string,
    module?: string,
    roomId?: number,
    userId?: string,
    eventType?: keyof ServerEmitEvents,
    details?: Record<string, unknown>,
) => {
    return new UnibabbleError(
        ErrSource.CLIENT,
        code,
        message,
        method,
        module,
        roomId,
        userId,
        eventType,
        details
    )
}

export const unknown__cliErr = (
    err: unknown,
    code?: ErrorCode,
    method?: string,
    module?: string,
    msg?: string
) => {
    return err instanceof UnibabbleError ?
        err :
        new UnibabbleError(
            ErrSource.CLIENT,
            code ?? ErrorCode.UNKNOWN_ERROR,
            (err as Error).message ?? msg ?? 'unknown client error, SEE details',
            method,
            module,
            undefined,
            undefined,
            undefined,
            { err }
        )
}

export const cliErr__errPayload = (
    err: UnibabbleError,
    method?: string,
    module?: string,
): ErrorPayload => {

    const me = method ? method + ' - ' : err.method ? err.method + ' - ' : '';
    const mo = module ? module + ' - ' : err.module ? err.module + ' - ' : '';
    logger.error(
        `Error in ${me}${err.eventType} operation - Code: ${err.code}${err.roomId ? `, Room: ${err.roomId}` : ''}`,
        {
            module: mo,
            method: me,
            timestamp: getTimestamp(),
            details: err.details
        }
    );

    return createErrorMessage(
        ErrSource.CLIENT,
        err.code,
        err.message,
        me,
        mo,
        err.roomId,
        err.userId,
        err.eventType,
        err.details
    );
};


// 服务器错误转换
export const createSerErr = (
    code: ErrorCode,
    message: string,
    method?: string,
    module?: string,
    roomId?: number,
    userId?: string,
    eventType?: keyof ClientEmitEvents,
    details?: Record<string, unknown>,
): UnibabbleError => {
    return new UnibabbleError(
        ErrSource.SERVER,
        code,
        message,
        method,
        module,
        roomId,
        userId,
        eventType,
        details
    );
}

export const unknown__serErr = (
    err: unknown,
    code?: ErrorCode,
    method?: string,
    module?: string,
    msg?: string
) => {
    return err instanceof UnibabbleError ?
        err :
        createSerErr(
            code ?? ErrorCode.UNKNOWN_ERROR,
            (err as Error).message ?? msg ?? 'unknown server error',
            method,
            module,
            undefined,
            undefined,
            undefined,
            { allMsg: JSON.stringify(err) }
        )
}

export const serErr__errPayload = (
    err: UnibabbleError,
    method?: string,
    module?: string,
) => {
    return createErrorMessage(
        ErrSource.SERVER,
        err.code,
        err.message,
        method ?? err.method,
        module ?? 'Server',
        err.roomId,
        err.userId,
        err.eventType,
        err.details,
    );
}
