import { RemoteSocket, Server } from 'socket.io';
import type { ServerEmitEvents, ServerSocketData, UserId } from '../types/index.js';
import { LanguageCode, LanguageName, AvailableLanguages } from '../types/index.js';
import { v4 as uuidv4 } from 'uuid';


/**
 * 格式化时间戳为本地时间字符串
 * @param timestamp 时间戳
 * @param options 格式化选项
 */
export function formatTime(
  timestamp: number,
  options: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit'
  }
): string {
  return new Date(timestamp).toLocaleTimeString(undefined, options);
}


/**
 * 获取当前时间戳
 */
export function getTimestamp(): number {
  return Date.now();
}


/**
 * 获取语言名称
 * @param langCode 语言代码
 * @returns 语言名称
 */
export function getLanguageName(langCode: LanguageCode): LanguageName {
  if (!AvailableLanguages.has(langCode)) {
    throw new Error(`Unsupported language code: ${langCode}`);
  }
  return AvailableLanguages.get(langCode) as LanguageName;
}

/**
 * 根据用户 ID 和房间 ID 找到对应的 Socket
 * @param io 
 * @param userId 
 * @param roomId 
 * @returns 
 */
export const findSocketByUserId = async (
  io: Server,
  userId: UserId,
  roomId?: number
): Promise<RemoteSocket<ServerEmitEvents, ServerSocketData> | undefined> => {
  const sockets = (() => {
    if (roomId) {
      return io.in(roomId.toString()).fetchSockets()
    } else {
      return io.sockets.fetchSockets()
    }
  })()

  return (await sockets).find(s => s.data.userId === userId)
}

export const uuid = () => {
  return uuidv4();
}