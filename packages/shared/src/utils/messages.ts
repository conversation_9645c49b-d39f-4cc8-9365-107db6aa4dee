import type { ErrorMessage, SystemMessage, ErrorPayload, SystemPayload } from '../types/index.js';
import { EMessageType, ErrSource, ErrorCode, ClientEmitEvents, ServerEmitEvents } from '../types/index.js'
import { getTimestamp } from './common.js'


export const unknown__Error = (err: unknown) => err instanceof Error ? err : new Error(JSON.stringify(err))

/**
 * 创建错误消息
 */
export function createErrorMessage(
  source: typeof ErrSource[keyof typeof ErrSource],
  code: ErrorCode,
  message: string,
  method?: string,
  moduleName?: string,
  roomId?: number,
  userId?: string,
  eventType?: keyof ServerEmitEvents | keyof ClientEmitEvents,
  details?: Record<string, unknown>
): ErrorPayload {

  const timestamp = getTimestamp()
  const errMsg: ErrorMessage = {
    id: genErrMsgId(roomId, userId),
    timestamp,
    content: message,
    code: code,
    type: EMessageType.ERROR
  }

  return {
    moduleName,
    eventType,
    timestamp,
    errMsg,
    roomId,
    userId,
    method,
    source,
    details
  };
}

/**
 * 创建通用错误消息
 */
export function createUnknownError(error: unknown, moduleName: string): ErrorPayload {
  const err = unknown__Error(error)
  return createErrorMessage(
    ErrSource.DEFAULT,
    ErrorCode.UNKNOWN_ERROR,
    err.message,
    undefined,
    moduleName,
    undefined,
    undefined,
    undefined,
    { err }
  );
}

/**
 * 创建系统消息
 */
export function createSystemMessagePayload(
  message: string,
  roomId: number,
  details?: Record<string, unknown>
): SystemPayload {
  const timestamp = getTimestamp()
  const sysMsg: SystemMessage = {
    id: genSysMsgId(roomId),
    timestamp,
    content: message,
    type: EMessageType.SYSTEM
  }

  return {
    timestamp,
    sysMsg,
    roomId,
    details,
  };
}

export const idSpliter = '^'

// 生成user消息ID
export const genUserMsgId = (roomId: number, userId: string): string => {
  if (!roomId) {
    throw new Error('roomId is required');
  }

  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 4);

  return [roomId, timestamp, userId, random].join(idSpliter)
};

// 生成sys消息ID
export const genSysMsgId = (roomId?: number): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 4);
  return [
    roomId ? roomId.toString() : '',
    timestamp,
    random
  ].filter(Boolean).join(idSpliter)
};

// 生成err消息ID
export const genErrMsgId = (roomId?: number, userId?: string): string => {
  return [
    roomId ? roomId.toString() : '',
    userId ? userId : '',
    getTimestamp().toString(36),
    Math.random().toString(36).substring(2, 4)
  ].filter(Boolean).join(idSpliter)
};


export const createSystemMessage = (
  message: string,
  roomId?: number
): SystemMessage => {
  return {
    id: genSysMsgId(roomId),
    timestamp: getTimestamp(),
    content: message,
    type: EMessageType.SYSTEM
  }
}