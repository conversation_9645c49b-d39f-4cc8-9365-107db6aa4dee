import { ErrorCode } from "./index.js";

export type UserLang = {
  source: LanguageSource;
  target: LanguageCode;
  name: LanguageName;
}

export enum UserRole {
  MEMBER,
  ADMIN
}

/**
 * 用户信息
 */
export interface User {
  id: string;
  name: string;
  lang: UserLang;
  email?: string;
}

export type UserId = User['id'];

export enum RoomActiveStatus {
  ACTIVE,
  INACTIVE,
  IDLE,
  BLOCKED
}

/**
 * 房间信息
 */
export interface Room {
  id: number;
  users: User[];
  createdAt: number;
  status: RoomActiveStatus;
  adminId: UserId;
}

/**
 * 基础消息接口
 */
export interface BaseMessage {
  /** 消息唯一标识 */
  id: string;
  /** 发送时间戳 */
  timestamp: number;
}

export enum EMessageType {
  TEXT,
  SYSTEM,
  ERROR
}

export enum EChatModeType {
  NORMAL,
  RANDOM
}

export enum EServerAckType {
  CONFIRM,
  REJECT
}

/**
 * 文本消息
 */
export interface TextMessage extends BaseMessage {
  /** 消息类型 */
  type: EMessageType.TEXT;
  /** 消息内容 */
  content: string;
  /** 房间唯一标识 */
  roomId: number;
  /** 发送者信息 */
  sender: User;
}

/**
 * 系统消息
 */
export interface SystemMessage extends BaseMessage {
  /** 消息类型 */
  type: EMessageType.SYSTEM;
  /** 消息内容 */
  content: string;
  /** 消息语言 */
  lang?: LanguageCode
}

/**
 * 错误消息
 */
export interface ErrorMessage extends BaseMessage {
  /** 消息类型 */
  type: EMessageType.ERROR;
  /** 消息内容 */
  content: string;
  /** 错误代码 */
  code: ErrorCode;
}

/**
 * 消息类型
 */
export type Message = TextMessageWithState | SystemMessage | ErrorMessage;

/**
 * 消息状态
 */
export enum EMessageState {
  SENDING,
  DELIVERED,
  ACK,
  RESENDING,
  FAILED
}

export interface MessageState {
  state: EMessageState;
  stateAt?: number;
  fstCheckAt?: number;
}

/**
 * 带状态的消息
 */
export interface TextMessageWithState extends TextMessage {
  state: MessageState;
  /** 翻译后的消息内容 */
  translatedContent?: string;
  targetLang?: LanguageCode;
}

export type FstCheckAt = number;

export const deeplLangs = [
  { "code": "BG", "target": "BG", "name": "Bulgarian", "originalLangName": "Bulgarian", "supports_formality": false },
  { "code": "CS", "target": "CS", "name": "Czech", "originalLangName": "Czech", "supports_formality": false },
  { "code": "DA", "target": "DA", "name": "Danish", "originalLangName": "Danish", "supports_formality": false },
  { "code": "DE", "target": "DE", "name": "German", "originalLangName": "Deutsch", "supports_formality": true },
  { "code": "EL", "target": "EL", "name": "Greek", "originalLangName": "Greek", "supports_formality": false },
  { "code": "EN", "target": "EN-GB", "name": "English (British)", "originalLangName": "English (British)", "supports_formality": false },
  { "code": "EN", "target": "EN-US", "name": "English (American)", "originalLangName": "English (American)", "supports_formality": false },
  { "code": "ES", "target": "ES", "name": "Spanish", "originalLangName": "Español", "supports_formality": true },
  { "code": "ET", "target": "ET", "name": "Estonian", "originalLangName": "Estonian", "supports_formality": false },
  { "code": "FI", "target": "FI", "name": "Finnish", "originalLangName": "Finnish", "supports_formality": false },
  { "code": "FR", "target": "FR", "name": "French", "originalLangName": "Français", "supports_formality": true },
  { "code": "HU", "target": "HU", "name": "Hungarian", "originalLangName": "Hungarian", "supports_formality": false },
  { "code": "ID", "target": "ID", "name": "Indonesian", "originalLangName": "Indonesian", "supports_formality": false },
  { "code": "IT", "target": "IT", "name": "Italian", "originalLangName": "Italiano", "supports_formality": true },
  { "code": "JA", "target": "JA", "name": "Japanese", "originalLangName": "日本語", "supports_formality": true },
  { "code": "KO", "target": "KO", "name": "Korean", "originalLangName": "Korean", "supports_formality": false },
  { "code": "LT", "target": "LT", "name": "Lithuanian", "originalLangName": "Lithuanian", "supports_formality": false },
  { "code": "LV", "target": "LV", "name": "Latvian", "originalLangName": "Latvian", "supports_formality": false },
  { "code": "NB", "target": "NB", "name": "Norwegian", "originalLangName": "Norwegian", "supports_formality": false },
  { "code": "NL", "target": "NL", "name": "Dutch", "originalLangName": "Nederlands", "supports_formality": true },
  { "code": "PL", "target": "PL", "name": "Polish", "originalLangName": "Polski", "supports_formality": true },
  { "code": "PT", "target": "PT-BR", "name": "Portuguese (Brazilian)", "originalLangName": "Português (Brasileiro)", "supports_formality": true },
  { "code": "PT", "target": "PT-PT", "name": "Portuguese (European)", "originalLangName": "Português (Europeu)", "supports_formality": true },
  { "code": "RO", "target": "RO", "name": "Romanian", "originalLangName": "Romanian", "supports_formality": false },
  { "code": "RU", "target": "RU", "name": "Russian", "originalLangName": "Русский", "supports_formality": true },
  { "code": "SK", "target": "SK", "name": "Slovak", "originalLangName": "Slovak", "supports_formality": false },
  { "code": "SL", "target": "SL", "name": "Slovenian", "originalLangName": "Slovenian", "supports_formality": false },
  { "code": "SV", "target": "SV", "name": "Swedish", "originalLangName": "Swedish", "supports_formality": false },
  { "code": "TR", "target": "TR", "name": "Turkish", "originalLangName": "Turkish", "supports_formality": false },
  { "code": "UK", "target": "UK", "name": "Ukrainian", "originalLangName": "Ukrainian", "supports_formality": false },
  { "code": "ZH", "target": "ZH-HANS", "name": "Chinese (simplified)", "originalLangName": "中文" }
] as const;

export const AvailableLanguages = deeplLangs.reduce((map, lang) => {
  map.set(lang.target, lang.originalLangName);
  return map;
}, new Map<string, string>());
export type LanguageCode = typeof deeplLangs[number]["target"];
export type LanguageName = typeof deeplLangs[number]["originalLangName"];
export type LanguageSource = typeof deeplLangs[number]["code"];

// 定义一个紧凑的用户语言类型
export type CompactUserLang = number;

