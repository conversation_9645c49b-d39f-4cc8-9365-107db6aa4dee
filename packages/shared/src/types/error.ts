import { ClientEmitEvents, ServerEmitEvents, ErrorCode } from "./index.js";

export const ErrSource = { CLIENT: 'CLIENT', SERVER: 'SERVER', DEFAULT: 'DEFAULT' } as const

export class UnibabbleError extends Error {
    constructor(
        public source: typeof ErrSource[keyof typeof ErrSource],
        public code: ErrorCode,
        public message: string,
        public method?: string,
        public module?: string,
        public roomId?: number,
        public userId?: string,
        public eventType?: keyof ServerEmitEvents | keyof ClientEmitEvents,
        public details?: Record<string, unknown>,
    ) {
        super(message);
        this.source = source;
        this.code = code;
        this.method = method;
        this.roomId = roomId;
        this.userId = userId;
        this.eventType = eventType;
        this.details = details;
        this.module = module;
    }
}

