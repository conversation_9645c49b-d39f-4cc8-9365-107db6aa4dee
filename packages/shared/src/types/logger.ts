/**
 * 日志级别枚举
 * - DEBUG: 调试信息，用于开发和排查问题
 * - INFO: 一般信息，记录正常的业务流程
 * - WARN: 警告信息，表示潜在的问题
 * - ERROR: 错误信息，表示需要立即处理的问题
 */
export type LogLevel = 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';

/**
 * 日志元数据接口
 * 用于存储额外的上下文信息
 */
export interface LogDetails {
  [key: string]: unknown;
}

/**
 * 日志上下文接口
 * 定义日志记录时需要携带的上下文信息
 */
export interface LogContext {
  /** 模块名称，标识日志来源的模块 */
  module: string;

  /** 时间戳，记录日志产生的时间 */
  timestamp: number;

  /** 日志消息 */
  message?: string;

  /** 方法名称，标识日志来源的具体方法 */
  method?: string;

  /** 消息ID，用于追踪特定消息的处理流程 */
  messageId?: string;

  /** 房间ID，用于关联房间相关的日志 */
  roomId?: number;

  /** 用户ID，用于关联用户相关的日志 */
  userId?: string;

  /** 错误对象，记录详细的错误信息 */
  error?: Error;

  /** 追踪ID，用于分布式系统中的请求追踪 */
  // traceId?: string;

  /** 元数据，存储其他自定义上下文信息 */
  details?: LogDetails;
}

/**
 * 日志记录器接口
 * 定义不同级别的日志记录方法
 */
export interface Logger {
  /**
   * 记录调试级别的日志
   * @param message 日志消息
   * @param context 日志上下文
   */
  debug: (message: string, context: LogContext) => void;

  /**
   * 记录信息级别的日志
   * @param message 日志消息
   * @param context 日志上下文
   */
  info: (message: string, context: LogContext) => void;

  /**
   * 记录警告级别的日志
   * @param message 日志消息
   * @param context 日志上下文
   */
  warn: (message: string, context: LogContext) => void;

  /**
   * 记录错误级别的日志
   * @param message 日志消息
   * @param context 日志上下文
   */
  error: (message: string, context: LogContext) => void;
}