import { LanguageCode, LanguageSource } from "./index.js";

export type TranslateLang = { source: string; target: string; name: string }

export interface DeepLPostBody {
    senderLangCode: LanguageSource,
    receiverLangCode: LanguageCode,
    content: string
}

export interface BaseTranslateResult {
    provider: string
}

export interface TranslateUsage extends BaseTranslateResult {
    limit?: number
    anyLimitReached?: boolean

    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
}

export interface TranslateResult extends BaseTranslateResult {
    content: string
    translated: string
    targetLang: LanguageCode
    sourceLang: LanguageSource
    usage?: TranslateUsage
}

export interface TranslateSupportLanguages extends BaseTranslateResult {
    langs: TranslateLang[]
}