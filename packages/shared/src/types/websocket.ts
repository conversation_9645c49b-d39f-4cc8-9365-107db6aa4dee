import { Socket as OriginalServerSocket } from 'socket.io';
import { Socket as OriginalClientSocket } from 'socket.io-client';
import {
  TextMessageWithState, User, Message, SystemMessage,
  ErrorMessage, RoomActiveStatus, UserLang, TranslateResult, ErrSource,
  Room,
  EChatModeType,
  EServerAckType
} from './index.js';


export const WebSocketEvents = {
  STANDARD: {
    SERVER: {
      //当一个新的客户端成功连接到服务端时触发。参数: socket: Socket, 代表与新连接的客户端的通信通道。可以通过 socket.id 获取客户端的唯一标识符。
      CONNECTION: 'connection', // 初始化客户端相关的状态、监听客户端事件、分配资源等。
      // 在 disconnect 事件之前触发，表示客户端即将断开连接。	参数 reason
      DISCONNECTING: 'disconnecting', // 进行最后的清理工作（此时连接可能仍然有效，但不保证消息能可靠送达）。
      // 客户端断开连接时触发。
      DISCONNECT: 'disconnect', // 清理客户端相关的资源、更新在线用户列表、通知其他客户端等。
    },
    CLIENT: {
      // 客户端成功连接到服务端时触发。
      CONNECT: 'connect', // 发送初始数据、开始监听服务端事件、更新 UI 状态等。
      // 客户端断开连接时触发。	参数 reason: string
      DISCONNECT: 'disconnect', // 清理客户端状态、显示断开连接提示、尝试重新连接（如果启用了自动重连）等。
      // 客户端连接到服务端失败时触发。 参数 error: Error
      CONNECT_ERROR: 'connect_error', // 显示连接错误信息、尝试重新连接（如果启用了自动重连）等。
      // 客户端正在尝试重新连接到服务端时触发（每次尝试都会触发）。 一般不与RECONNECTING同时出现 参数 attamptNum: number
      RECONNECT_ATTEMPT: 'reconnect_attempt', // 显示正在重连的提示、更新重连尝试次数等。
      // 与reconnect_attempt类似，但更早，表示客户端已经决定要进行重连，但还没开始尝试。参数 attamptNum: number
      RECONNECTING: 'reconnecting',
      // 客户端重新连接到服务端失败时触发。	参数 error: Error
      RECONNECT_ERROR: 'reconnect_error', // 显示重连错误信息、可能需要根据错误类型采取不同的处理措施。
      // 客户端在达到最大重连尝试次数后仍然无法连接到服务端时触发。
      RECONNECT_FAILED: 'reconnect_failed', // 显示重连失败的提示、停止自动重连、可能需要用户手动触发重连等。
      // 客户端成功重新连接到服务端时触发。
      RECONNECT: 'reconnect', // 恢复之前的状态、重新订阅事件、更新 UI 状态等。
      // 客户端收到服务端发送的 ping 心跳包时触发。
      PING: 'ping', // 通常无需手动处理，Socket.IO 内部会自动响应 pong。
      // 客户端收到服务端对客户端发送的 ping 的响应（pong）时触发。
      PONG: 'pong', // 通常无需手动处理
    },
  },
  ROOM: {
    UPDATE: 'room:update',
    JOIN: 'room:join',
    JOINED: 'room:joined',
    LEAVE: 'room:leave',
    LEFT: 'room:leaved',
    REENTER: 'room:reenter',
    RANDOM: {
      MATCH: 'room:random:match',
      ACK: 'room:random:ack'
    },
  },
  MESSAGE: {
    SEND: {
      SENDING: 'message:send',
      DELIVERED: 'message:delivered',
      ACK: 'message:ack',
    },
    HISTORY: {
      SHARE: 'message:history:share',
      OFFER: 'message:history:offer',
    },
    TRANSLATE: 'message:translate',
  },
  USER: {
    KICK: 'user:kick',
    KICKED: 'user:kicked',
  },
  WARNNING: {
    ROOM: {
      IDLE: 'warnning:room:idle',
    },
  },
  ACTION: {
    SERVER: {
      FORCEDISCONNECT: 'action:server:forceDisconnect',
      INVITE: 'action:server:invite'
    },
  },
  ERROR: {
    ROOM: {
      JOIN: 'error:room:join',
      FULL: 'error:room:full',
      LEAVE: 'error:room:leave',
      NOT_FOUND: 'error:room:not_found',
      kICK: 'error:room:kick',
    },
    MESSAGE: {
      SEND_FAILED: 'error:message:send_failed',
      DELIVERED_FAILED: 'error:message:delivered_failed',
    },
    USER: {
      CONNECT: 'error:user:connect',
      NOT_EXISTS: 'error:user:not_exists',
    },
    UNKNOWN: 'error:unknown',
    SERVER: {
      STATUS: 'error:server:status',
      MATCH_RANDOM: 'error:server:match_random'
    }
  },
  SYSTEM: 'system',
  SETTINGS:'setting',
  SERVER: {
    STATUS: 'server:status'
  }
} as const;



/**
 * 用户状态
 */
export enum UserStatus {
  ONLINE = 'online',
  OFFLINE = 'offline'
}

/**
 * 基础负载接口
 */
export interface BasePayload {
  timestamp: number;
}

export interface AckPayload extends BasePayload {
  ack: EServerAckType;
}

export interface BaseUserPayload extends BasePayload {
  userId: string;
  userName?: string;
}

/**
 * 基础房间负载接口
 */
export interface BaseRoomPayload extends BasePayload {
  roomId: number;
}

/**
 * 基础用户房间负载接口
 */
export interface BaseUserRoomPayload extends BaseRoomPayload {
  user: User;
}

/**
 * 加入房间请求负载
 */
export interface JoinRoomPayload extends BaseUserRoomPayload {
  userToken: string
  chatMode: EChatModeType
  reenter?: boolean
  isInvitor?: boolean
}

/**
 * 离开房间请求负载
 */
export interface LeaveRoomPayload extends BaseUserRoomPayload {
  isClient?: boolean;
  isServerLeave?: boolean;
  isForceLeave?: boolean;
  requesterId?: string;
}

export interface InvitePayload extends BaseUserPayload {
  invitorLang?: UserLang;
  retry?: number
}


export interface KickUserPayload extends BaseUserRoomPayload {
  kickee: string[];
  userToken: string;
}

export interface UserKickedPayload extends BaseUserRoomPayload {
  isKicked: boolean;
  kickee: string;
}

/**
 * 房间更新响应负载
 */
export interface RoomUpdatedPayload extends BaseRoomPayload {
  users: User[];
  status: RoomActiveStatus;
  createdAt?: number;
  adminId?: string;
  lastActiveAt?: number;

  // for broadcast
  joinedUserId?: string;
  leavedUserId?: string;
  kickedUserIds?: string[]
}

export interface UserLeftPayload extends BaseRoomPayload {
  leftUserId?: string;
  isLeft?: boolean;
}

// client receive 2 types of payload: join and update
export interface UserJoinedPayload extends BasePayload {
  room: Room;
  joinedUserId: string;
  isJoin: boolean;
  userToken: string;
}


/**
 * 发送消息请求负载
 */
export interface SendMessagePayload extends BaseRoomPayload {
  userId: string;
  content: string;
  lang: UserLang;
  messageId?: string;
  fstSendTimestamp?: number;
}

/**
 * server status for REP to client 
 */
export interface ServerStatusPayload extends BaseUserPayload, AckPayload {
  population: number,
  randomModeCount: number
}
export interface ServerMatchRandomPayload extends BaseRoomPayload,AckPayload {
  isInvitor: boolean,
  userToken?: string
}

export interface DeliveredMessageAckPayload extends BaseRoomPayload {
}

/**
 * 新消息响应负载
 */
export interface MessageDeliveredPayload extends BaseUserRoomPayload {
  messageWithState: TextMessageWithState;
  // fstSendTimestamp?: number;
}

/**
 * 消息历史负载
 */
export interface HistorySharePayload extends BaseRoomPayload {
  fromUserId: string;
  targetUserId: string;
  histories: Message[];
}

/**
 * 消息翻译负载
 */
export interface TranslatePayload extends BaseRoomPayload {
  sender: User;
  receiver: User;
  record: TranslateResult;
}

export interface ConfirmPayload extends BasePayload {
  type: 'confirm';
  messageId: string;
}

/**
 * 错误响应负载
 */
export interface ErrorPayload extends BasePayload {
  errMsg: ErrorMessage;
  source?: typeof ErrSource[keyof typeof ErrSource];
  eventType?: keyof ServerEmitEvents | keyof ClientEmitEvents;
  method?: string;
  moduleName?: string;
  roomId?: number;
  userId?: string;
  details?: Record<string, unknown>;
}


/**
 * 系统消息响应负载
 */
export interface SystemPayload extends BaseRoomPayload {
  sysMsg: SystemMessage;
  details?: Record<string, unknown>
}

/**
 * client settings
 */
export interface ClientPayload extends BaseUserPayload {
  chatMode: EChatModeType;
  userToken?: string;
  details?: Record<string, unknown>;
}

export interface EmptyPayload { }


/**
 * WebSocket事件类型统一定义
 */
export type UnibabbleWebSocketEventMap = {
  // client emit
  [WebSocketEvents.ROOM.JOIN]: {
    req: JoinRoomPayload;
    res: void;
  };
  [WebSocketEvents.ROOM.LEAVE]: {
    req: LeaveRoomPayload;
    res: void;
  };
  [WebSocketEvents.ROOM.REENTER]: {
    req: JoinRoomPayload;
    res: void;
  };
  [WebSocketEvents.MESSAGE.SEND.SENDING]: {
    req: SendMessagePayload;
    res: void;
  };
  [WebSocketEvents.MESSAGE.SEND.ACK]: {
    req: DeliveredMessageAckPayload;
    res: void;
  };
  [WebSocketEvents.MESSAGE.TRANSLATE]: {
    req: TranslatePayload;
    res: void;
  };
  [WebSocketEvents.SETTINGS]: {
    req: ClientPayload;
    res: AckPayload;
  };
  [WebSocketEvents.SERVER.STATUS]: {
    req: ClientPayload;
    res: ServerStatusPayload;
  };
  [WebSocketEvents.USER.KICK]: {
    req: KickUserPayload;
    res: void;
  };
  [WebSocketEvents.ROOM.RANDOM.MATCH]: {
    req: ClientPayload;
    res: void;
  };

  // client listen self
  [WebSocketEvents.STANDARD.CLIENT.RECONNECTING]: {
    req: void;
    res: number;
  };

  [WebSocketEvents.STANDARD.CLIENT.RECONNECT_FAILED]: {
    req: void;
    res: EmptyPayload;
  };

  [WebSocketEvents.STANDARD.CLIENT.RECONNECT_ERROR]: {
    req: void;
    res: Error;
  };

  [WebSocketEvents.STANDARD.CLIENT.RECONNECT]: {
    req: void;
    res: EmptyPayload;
  };

  [WebSocketEvents.STANDARD.CLIENT.CONNECT_ERROR]: {
    req: void;
    res: Error;
  };

  [WebSocketEvents.STANDARD.CLIENT.PING]: {
    req: void;
    res: EmptyPayload;
  };
  [WebSocketEvents.STANDARD.CLIENT.PONG]: {
    req: void;
    res: number;
  };

  // server emit
  [WebSocketEvents.ROOM.UPDATE]: {
    req: void;
    res: RoomUpdatedPayload;
  };
  [WebSocketEvents.ROOM.JOINED]: {
    req: void;
    res: UserJoinedPayload;
  };
  [WebSocketEvents.ROOM.LEFT]: {
    req: void;
    res: UserLeftPayload;
  };
  [WebSocketEvents.MESSAGE.SEND.DELIVERED]: {
    req: void;
    res: MessageDeliveredPayload;
  };
  [WebSocketEvents.MESSAGE.HISTORY.SHARE]: {
    req: HistorySharePayload;
    res: void;
  };
  [WebSocketEvents.MESSAGE.HISTORY.OFFER]: {
    req: void;
    res: HistorySharePayload;
  };
  [WebSocketEvents.SYSTEM]: {
    req: void;
    res: SystemPayload;
  };
  [WebSocketEvents.USER.KICKED]: {
    req: void;
    res: UserKickedPayload;
  };
  [WebSocketEvents.ACTION.SERVER.INVITE]: {
    req: AckPayload;
    res: InvitePayload;
  };
  [WebSocketEvents.ROOM.RANDOM.ACK]: {
    req: void;
    res: ServerMatchRandomPayload;
  };

  // server emit ERROR
  [WebSocketEvents.ERROR.ROOM.FULL]: {
    req: void;
    res: ErrorPayload;
  };
  [WebSocketEvents.ERROR.ROOM.JOIN]: {
    req: void;
    res: ErrorPayload;
  };
  [WebSocketEvents.ERROR.ROOM.LEAVE]: {
    req: void;
    res: ErrorPayload;
  };
  [WebSocketEvents.ERROR.MESSAGE.SEND_FAILED]: {
    req: void;
    res: ErrorPayload;
  };
  [WebSocketEvents.ERROR.USER.CONNECT]: {
    req: void;
    res: ErrorPayload;
  };
  [WebSocketEvents.ERROR.UNKNOWN]: {
    req: void;
    res: ErrorPayload;
  };
  [WebSocketEvents.ERROR.ROOM.NOT_FOUND]: {
    req: void;
    res: ErrorPayload;
  };
  [WebSocketEvents.ERROR.USER.NOT_EXISTS]: {
    req: void;
    res: ErrorPayload;
  };
  [WebSocketEvents.ERROR.SERVER.STATUS]: {
    req: void;
    res: ErrorPayload;
  };
  [WebSocketEvents.ERROR.SERVER.MATCH_RANDOM]: {
    req: void;
    res: ErrorPayload;
  };

  // WARNNING
  [WebSocketEvents.WARNNING.ROOM.IDLE]: {
    req: void;
    res: SystemPayload;
  };

  // ACTION
  [WebSocketEvents.ACTION.SERVER.FORCEDISCONNECT]: {
    req: void;
    res: SystemPayload;
  };

};


/* server */
export type ServerEmitEvents = {
  [E in keyof UnibabbleWebSocketEventMap as UnibabbleWebSocketEventMap[E]['req'] extends void ? E : never]: (payload: UnibabbleWebSocketEventMap[E]['res']) => void;
} & {
  [E in keyof UnibabbleWebSocketEventMap as UnibabbleWebSocketEventMap[E]['req'] extends AckPayload ? E : never]: (payload: UnibabbleWebSocketEventMap[E]['res'], callback: (ack: AckPayload) => void) => void;
}

type ServerListenEvents = {
  [E in keyof UnibabbleWebSocketEventMap as UnibabbleWebSocketEventMap[E]['res'] extends void ? E : never]: (payload: UnibabbleWebSocketEventMap[E]['req']) => void;
} & {
  [E in keyof UnibabbleWebSocketEventMap as UnibabbleWebSocketEventMap[E]['res'] extends AckPayload ? E : never]: (payload: UnibabbleWebSocketEventMap[E]['req'], callback: (ack: AckPayload) => void) => void;
}


export interface ServerSocketData {
  roomId?: number;
  userId: string;
  userName?: string;
  email?: string;
  userToken?: string;
  lang?: number;
}

interface ServerSocketEvents {
  join: (room: string) => void;
  leave: (room: string) => void;
  to: (room: string) => ServerSocket;
  // ... 其他 socket.io 服务端方法
}

export interface ServerSocket extends OriginalServerSocket<
  ServerListenEvents,
  ServerEmitEvents,
  ServerSocketEvents,
  ServerSocketData
> { }


/* client */
export type ClientListenEvents = {
  [E in keyof UnibabbleWebSocketEventMap as UnibabbleWebSocketEventMap[E]['req'] extends void ? E : never]: (payload: UnibabbleWebSocketEventMap[E]['res']) => void;
} & {
  [E in keyof UnibabbleWebSocketEventMap as UnibabbleWebSocketEventMap[E]['req'] extends AckPayload ? E : never]: (payload: UnibabbleWebSocketEventMap[E]['res'], callback: (ack: AckPayload) => void) => void;
}

// export type ClientEmitEvents = {
//   [E in keyof UnibabbleWebSocketEventMap as UnibabbleWebSocketEventMap[E]['res'] extends void ? E : never]: (payload: UnibabbleWebSocketEventMap[E]['req']) => void;
// } & {
//   [E in keyof UnibabbleWebSocketEventMap as UnibabbleWebSocketEventMap[E]['res'] extends AckPayload? E : never]: (payload: UnibabbleWebSocketEventMap[E]['req'], callback: (ack: AckPayload) => void) => void;
// }

export type ClientEmitEvents = {
  [E in keyof UnibabbleWebSocketEventMap]: 
    UnibabbleWebSocketEventMap[E]['res'] extends void
      ? (payload: UnibabbleWebSocketEventMap[E]['req']) => void
      : (payload: UnibabbleWebSocketEventMap[E]['req'], callback: (ack: UnibabbleWebSocketEventMap[E]['res']) => void) => void;
};

export interface ClientSocket extends OriginalClientSocket<ClientListenEvents, ClientEmitEvents> { }
