{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["src/**/*.vue", "src/**/*.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"types": ["element-plus/global"], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@unibabble/shared": ["../shared/dist"]}, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true, "inlineSourceMap": false, "inlineSources": true, "declaration": true, "declarationMap": true}}