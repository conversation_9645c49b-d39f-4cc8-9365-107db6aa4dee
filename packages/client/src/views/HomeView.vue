<template>
  <div class="chat-view">
    <header class="header">
      <div class="header-left">
        <h1 @click="goHome" class="clickable-title">
          {{ cliConfig.app.title }}
        </h1>

        <div v-if="!currentUser" id="google-btn" @click="login_GoogleGISOrLocal">Login with Google</div>

        <el-dropdown v-if="currentUser" trigger="click" class="user-dropdown">
          <div class="user-avatar">
            <el-avatar :size="32" class="user-avatar-icon">
              {{ currentUser.name!.charAt(0).toUpperCase() }}
            </el-avatar>
            <span class="user-name">{{ currentUser.name }}</span>
            <el-icon class="el-icon--right">
              <ArrowDown />
            </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handleLogout">
                <el-icon>
                  <SwitchButton />
                </el-icon>
                <span>Logout</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="header-right">
        <div class="connection-status-toolbar">
          <div :class="['connection-status', { 'connection-status--connected': isConnected }]">
            {{ connectionStatus }}
          </div>
          <el-popover
            ref="popoverRef"
            v-model:visible="isExpanded"
            placement="bottom"
            :width="300"
            :trigger="[]"
            :show-arrow="false"
            popper-class="introduction-popover"
            :offset="10"
            @show="handlePopoverShow"
            @hide="handlePopoverHide"
          >
            <div class="introduction">
              <h3>Welcome to UniBabble</h3>
              <p>Born from my passion for connecting people, this project enables seamless cross-lingual conversations. Speak your language, connect with the world.</p>

              <p class="support-text">
                <i>If you find it interesting, please buy me a coffee:</i>
              </p>

              <p class="donation-text">
                USDT (Solana):
                <el-tooltip content="Click to copy" placement="top">
                  <span
                    class="wallet-address"
                    @click="copyToClipboard('GTBJRsWNyvWHkADdnjFwWuFxsagjXQLofYFdEqtmmsbz')"
                  >
                    GTBJRsWNyvWHkADdnjFwWuFxsagjXQLofYFdEqtmmsbz
                    <el-icon class="copy-icon"><Files /></el-icon>
                  </span>
                </el-tooltip>
              </p>

              <p class="feedback-text">
                <i>Feedback: <EMAIL></i>
              </p>
            </div>
            <template #reference>
              <div
                ref="referenceRef"
                class="toggle-button toggle-button--header"
                @mouseenter="isExpanded = true"
                @click.stop
                style="cursor: pointer"
              >
                <div class="coffee-container">
                  <div class="coffee-square">
                  </div>
                  <div class="coffee-glow"></div>
                </div>
              </div>
            </template>
          </el-popover>
        </div>
      </div>
    </header>

    <div v-if="!currentUser && enableAuth" class="chat-view__setup">
      <p>Please login with Google to start chatting.</p>
    </div>

    <el-dialog
      v-model="showInviteDialog"
      title="Invitation"
      width="400px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
      class="invite-dialog"
    >
      <div class="invite-content">
        <p>{{ chatStore.inviteInfo }}</p>
      </div>
      <div class="countdown-progress">
        <div 
          class="progress-bar" 
          :style="{
            width: `${(countdown / 10) * 100}%`,
            backgroundColor: `hsl(${120 * (countdown / 10)}, 100%, 45%)`
          }"
        ></div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleReject" :disabled="isProcessing">Reject</el-button>
          <el-button type="primary" @click="handleAccept" :loading="isProcessing">
            Accept
          </el-button>
        </div>
      </template>
    </el-dialog>

    <div v-if="!isChatRoomMounted && !currentRoom" class="chat-view__setup">
      <div class="setup-content">
        <h2>Your Language</h2>
        <div class="button-row">
          <LanguageSelector />
          <div class="mode-selector">
            <el-switch v-model="isRandom" />
            <span>Random</span>
            <el-tooltip 
              effect="dark" 
              content="Chat with random users in random mode"
              placement="top"
            >
              <el-icon class="info-icon"><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
          <el-button type="primary" @click="handleConnect2Room()" :loading="isConnecting">
            {{ startBtnName }}
          </el-button>
        </div>
      </div>
      <div>
        <!-- server connected user number , when this page is showing-->
        Total: {{ chatStore.population }} | Random: {{ chatStore.randomPopulation }}
      </div>
    </div>

    <div v-else-if="isConnected && !currentRoom" class="chat-view__loading">
      <el-icon class="is-loading">
        <el-loading />
      </el-icon>
      <p>Loading user data...</p>
    </div>

    <ChatRoom
      v-else-if="currentUser && currentRoom"
      v-model:isMounted="isChatRoomMounted"
      v-model:urlRoomId="urlRoomId"
      @error="logAndMarkError"
      @reenter="handleConnect2Room"
    />

    <UsernameDialog v-model="showUsernameDialog" @confirm="handleUsernameChange" />

    <el-dialog v-model="showError" title="Error" width="30%">
      <span>{{ errorMessage }}</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showError = false">Close</el-button>
          <el-button type="primary" @click="handleRetry">Retry</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { Files, InfoFilled } from '@element-plus/icons-vue';

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      ElMessage.success('Wallet address copied to clipboard!');
    } catch (err) {
      console.error('Failed to copy text: ', err);
      ElMessage.error('Failed to copy wallet address');
    }
  };
  import { ChatRoom, UsernameDialog, LanguageSelector } from '@/components';
  import { UserService } from '@/services';
  import { useChatStore } from '@/stores';
  import { cliConfig } from '@/config';
  import { getTimestamp, logger, unknown__cliErr, EChatModeType, EServerAckType } from '@unibabble/shared';
  import { computed, ref, onMounted, onBeforeUnmount, onUnmounted, watch } from 'vue';
  import _ from 'lodash';
  import router from '@/router';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { ArrowDown, SwitchButton } from '@element-plus/icons-vue';

  declare global {
    interface Window {
      google: any;
    }
  }

  const MODULE_NAME = 'client:HomeView';

  const route = useRoute();
  const chatStore = useChatStore();

  // 邀请相关状态
  const showInviteDialog = ref(false);
  const countdown = ref(10);
  const isProcessing = ref(false);
  let countdownTimer: number | null = null;

  const roomId = ref<string>('');

  // 监听路由参数中的roomId
  watch(
    () => route.params.roomId,
    newRoomId => {
      roomId.value = (newRoomId as string) || '';
    },
    { immediate: true }
  );

  const urlRoomId = computed({
    get: () => roomId.value,
    set: (value: string) => {
      roomId.value = value;
      if (value === '') {
        router.replace({ params: { roomId: undefined } });
      } else {
        route.params.roomId = value;
      }
    },
  });

  // 监听随机匹配的房间ID
  watch(() => chatStore.randomRoomId, (newRoomId) => {
      if (newRoomId) {
        urlRoomId.value = newRoomId.toString();
      } else {
        urlRoomId.value = '';
      }
    }
  );


  const isRandom = ref(chatStore.chatMode === EChatModeType.RANDOM);

  // 使用防抖函数处理模式切换
  const debouncedSwitchMode = _.debounce(async (newValue: boolean) => {

    console.log('debouncedSwitchMode', newValue);
    const last = chatStore.chatMode;
    try {
      chatStore.chatMode = newValue ? EChatModeType.RANDOM : EChatModeType.NORMAL;
      if (!await chatStore.switchChatMode(newValue)) {
        chatStore.chatMode = last;
        isRandom.value = last === EChatModeType.RANDOM;
      }
    } catch (error) {
      console.error('Failed to switch chat mode:', error);
      chatStore.chatMode = last;
      isRandom.value = last === EChatModeType.RANDOM;
    } 
  }, 300, { leading: true, trailing: false });

  // 监听 isRandom 变化并触发防抖函数
  watch(isRandom, (newValue) => {
    debouncedSwitchMode(newValue);
  });

  // 组件卸载时取消防抖函数
  onUnmounted(() => {
    debouncedSwitchMode.cancel();
  });

  // 监听 inviteInfo 变化
  watch(() => chatStore.inviteInfo, (newVal) => {
    if (newVal) {
      showInviteDialog.value = true;
      startCountdown();
    } else {
      showInviteDialog.value = false;
      resetCountdown();
    }
  });
  
  // 开始倒计时
  const startCountdown = () => {
    countdown.value = 10;
    isProcessing.value = false;
    
    if (countdownTimer) {
      clearInterval(countdownTimer);
    }
    
    countdownTimer = window.setInterval(() => {
      countdown.value--;
      
      if (countdown.value <= 0) {
        handleReject();
      }
    }, 1000);
  };
  
  // 重置倒计时
  const resetCountdown = () => {
    if (countdownTimer) {
      clearInterval(countdownTimer);
      countdownTimer = null;
    }
    isProcessing.value = false;
  };
  
  // 处理接受邀请
  const handleAccept = () => {
    isProcessing.value = true;
    chatStore.inviteInfo = null;
    chatStore.inviteAck = EServerAckType.CONFIRM;
  };
  
  // 处理拒绝邀请
  const handleReject = () => {
    isProcessing.value = true;
    chatStore.inviteInfo = null;
    chatStore.inviteAck = EServerAckType.REJECT;
  };
  
  // 组件卸载时清理定时器
  onBeforeUnmount(() => {
    resetCountdown();
  });

  const startBtnName = computed(() => {
    return urlRoomId.value ? 'Join' : 'Start';
  });
  const username = computed({
    get: UserService.getUsername,
    set: (uname: string) => UserService.setUsername(uname),
  });
  const showUsernameDialog = ref(false);
  const showError = ref(false);
  const errorMessage = ref('');

  let isConnected = computed({
    set: (v: boolean) => (chatStore.isConnected = v),
    get: () => chatStore.isConnected,
  });
  let isConnecting = computed({
    set: (v: boolean) => (chatStore.isConnecting = v),
    get: () => chatStore.isConnecting,
  });

  const currentUser = computed(() => chatStore.currentUser);
  const currentRoom = computed(() => chatStore.currentRoom);

  const connectionStatus = computed(() => {
    if (isConnected.value) return 'Connected';
    if (isConnecting.value) return 'Connecting...';
    return 'Not connected';
  });

  const enableAuth = computed(() => cliConfig.auth.enable);

  const isChatRoomMounted = ref(false);
  const isExpanded = ref(false);
  const isHovered = ref(false);
  const popoverRef = ref<HTMLElement | null>(null);
  const referenceRef = ref<HTMLElement | null>(null);

  // 点击外部关闭弹窗
  const handleClickOutside = (event: MouseEvent) => {
    if (
      isExpanded.value &&
      popoverRef.value &&
      referenceRef.value &&
      !popoverRef.value.contains(event.target as Node) &&
      !referenceRef.value.contains(event.target as Node)
    ) {
      isExpanded.value = false;
    }
  };

  onMounted(() => {
    document.addEventListener('click', handleClickOutside);
    
    // 每3分钟检查一次服务器状态
    const intervalId = setInterval(() => {
      if (currentUser.value) {
        chatStore.checkServerStatus(chatStore.chatMode);
      }
    // }, 3 * 60 * 1000); // 3分钟
    }, 1000); // 1秒

    // 组件卸载时清除定时器
    onUnmounted(() => {
      clearInterval(intervalId);
    });
  });

  onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside);
  });

  const handlePopoverShow = () => {
    // 处理弹窗显示
  };

  const handlePopoverHide = () => {
    // 处理弹窗隐藏
    isHovered.value = false;
  };

  chatStore.$subscribe(() => {
    console.log('chatStore.error', chatStore.error);
    console.log('chatStore.currentUser', chatStore.currentUser);
    console.log('chatStore.currentRoom', chatStore.currentRoom);
    console.log('chatStore.messages', chatStore.messages);
    console.log('chatStore.isConnected', chatStore.isConnected);
    console.log('isChatRoomMounted', isChatRoomMounted.value);
    console.log('selectedSourceLang', chatStore.selectedSourceLangCode);
    console.log('selectedTargetLang', chatStore.selectedTargetLangCode);
    console.log('auth enable', enableAuth);
    console.log('chat mode is random:', chatStore.chatMode === EChatModeType.RANDOM);
  });

  watch(
    () => chatStore.error,
    newError => {
      if (newError) {
        ElNotification({
          title: 'Error',
          message: newError.errMsg.content,
          type: 'error',
          duration: 10000,
          showClose: true,
          position: 'top-right',
          onClose: () => {
            chatStore.error = null;
          },
        });
      }
    }
  );

  // funcs
  const goHome = async () => {
    const message = isChatRoomMounted.value ? 'Confirm to go home and leave the room?' : 'Confirm to go home?';

    if (confirm(message)) {
      try {
        if (isChatRoomMounted.value) {
          await chatStore.leaveRoom();
        }
      } finally {
        router.replace('/');
      }
    }
  };

  const logAndMarkError = (errStr: string, module: string, userId?: string) => {
    logger.error(errStr, {
      module: module,
      method: 'logAndMarkError',
      userId: userId,
      timestamp: getTimestamp(),
    });

    errorMessage.value = errStr;
    showError.value = true;
  };

  const handleUsernameChange = (newUsername: string) => {
    logger.info('Username changed', {
      module: MODULE_NAME,
      method: 'handleUsernameChange',
      userId: currentUser.value?.id,
      timestamp: Date.now(),
      details: {
        oldUsername: username.value,
        newUsername,
      },
    });

    UserService.setUsername(newUsername);
    username.value = newUsername;
  };

  // start / join / reenter
  const _handleConnect2Room = async (
    cleanRoomWhenJoinFailed = true,
    isReenter = false,
    errMsg = 'Failed to connect to server',
    moduleName?: string
  ) => {
    if (!isChatRoomMounted.value) {
      chatStore.currentRoom = null;
      chatStore.messages.clear();
      chatStore.isConnected = false;
      chatStore.isConnecting = false;
      chatStore.error = null;
      chatStore.isInRoom = false;
    }

    const module = moduleName ?? MODULE_NAME;

    const errCallback = (err?: unknown) => {
      isConnecting.value = false;
      isConnected.value = false;
      logAndMarkError(errMsg, module, currentUser.value?.id);
    };

    const succCallback = () => {
      logger.info('Successfully connected and joined room', {
        module: module,
        timestamp: getTimestamp(),
        roomId: parseInt(urlRoomId.value, 10),
        userId: currentUser.value?.id,
      });
    };

    try {
      if (await chatStore.connect()) {

        if (chatStore.chatMode === EChatModeType.RANDOM && roomId.value === '') {
          await chatStore.matchRandom();
        } else {
          await chatStore.joinRoom(
            currentRoom.value?.id || parseInt(roomId.value, 10),
            cleanRoomWhenJoinFailed,
            isReenter
          );
        }

        if (chatStore.isConnected && chatStore.currentRoom && succCallback) succCallback();
        else if (errCallback) errCallback();
      }
    } catch (err) {
      logger.error('Client Connects failed', {
        module: module,
        method: 'handleConnect2Room',
        timestamp: getTimestamp(),
        details: {
          sourceLanguage: chatStore.selectedSourceLangCode,
          targetLanguage: chatStore.selectedTargetLangCode,
          roomId: urlRoomId.value,
          ...unknown__cliErr(err),
        },
      });
      isConnecting.value = false;
      // if (errCallback) errCallback(err)
    }
  };

  const _handleRetry = async () => {
    logger.info('Retrying connection', {
      module: MODULE_NAME,
      method: 'handleRetry',
      timestamp: getTimestamp(),
      roomId: parseInt(urlRoomId.value, 10),
    });

    showError.value = false;

    handleConnect2Room();
  };

  const handleRetry = _.debounce(_handleRetry, 300);
  const handleConnect2Room = _.debounce(_handleConnect2Room, 300);

  const handleLogout = async () => {
    try {
      // 如果在聊天室中，直接提示将退出房间
      if (isChatRoomMounted.value) {
        await ElMessageBox.confirm('退出登录将同时退出当前聊天室，是否继续？', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        });
      } else {
        // 如果不在聊天室中，显示普通的退出确认
        await ElMessageBox.confirm('确认退出登录吗？', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'info',
        });
      }

      // 用户确认后执行退出操作
      await chatStore.handleLogout();
      ElMessage.success('退出登录成功');
    } catch (error) {
      // 如果用户取消了确认对话框，error 会是 'cancel'
      if (error !== 'cancel') {
        ElMessage.error('退出登录失败');
      }
    }
  };

  const login_GoogleGISOrLocal = async () => {
    if (enableAuth.value) {
      window.google.accounts.id.initialize({
        client_id: cliConfig.auth.google.clientId,
        callback: (response: any) => {
          console.log('GIS response:', response);
          chatStore.handleGoogleLogin(response.credential);
        },
        auto_select: false,
        prompt: 'select_account',
      });
      window.google.accounts.id.renderButton(document.getElementById('google-btn'), {
        theme: 'outline',
        size: 'large',
      });
      window.google.accounts.id.prompt(); // 触发 One Tap
    } else {
      chatStore.createUser();
    }

    // do connect
    await chatStore.connect();
  };

  // const googleGIS = () => {
  //   if (enableAuth) {
  //     window.google.accounts.id.initialize({
  //       client_id: cliConfig.auth.google.clientId,
  //       callback: (response: any) => {
  //         console.log('GIS response:', response);
  //         chatStore.handleGoogleLogin(response.credential)
  //       },
  //       auto_select: false,
  //       prompt: 'select_account',
  //     });
  //     window.google.accounts.id.renderButton(
  //       document.getElementById('google-btn'),
  //       { theme: 'outline', size: 'large' }
  //     );
  //     window.google.accounts.id.prompt(); // 触发 One Tap
  //   }
  // }

  onMounted(() => {
    // console.log('cliConfig:', cliConfig);
    // showClientStateStats();
    // googleGIS()
  });
</script>

<style scoped>
  .mode-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 16px;
  }

  .mode-selector span {
    font-size: 14px;
    color: var(--el-text-color-regular);
  }

  .info-icon {
    color: var(--el-color-info);
    cursor: help;
  }

  .random-mode-description {
    margin-top: 8px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
    text-align: center;
    padding: 4px 0;
  }

  .toggle-button {
    position: absolute;
    left: -40px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: none;
    z-index: 10;
    border: none;
    transition: transform 0.3s;
  }

  .connection-status-toolbar {
    display: flex;
    align-items: center;
    gap: 14px;
  }

  .toggle-button--header {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  .coffee-container {
    position: relative;
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .coffee-square {
    position: relative;
    z-index: 2;
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.1),
      0 4px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .coffee-container:hover .coffee-square {
    transform: translateY(-2px) rotate(2deg);
    box-shadow:
      0 6px 12px rgba(0, 0, 0, 0.15),
      0 8px 16px rgba(0, 0, 0, 0.1);
  }

  .coffee-square svg {
    width: 24px;
    height: 24px;
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }

  .coffee-container:hover .coffee-square svg {
    transform: scale(1.1) rotate(-5deg);
  }

  .coffee-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 33px;
    height: 33px;
    border-radius: 12px;
    background: conic-gradient(
      from 0deg,
      #ff7f00,
      #ffff00 15%,
      #00ff00 30%,
      #00bfff 45%,
      #8a2be2 60%,
      #ff1493 75%,
      #ff7f00 90%
    );
    opacity: 0.7;
    filter: blur(8px);
    transform: translate(-50%, -50%) scale(1);
    animation:
      coffee-glow-spin 6s linear infinite,
      coffee-glow-pulse 3s ease-in-out infinite alternate;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
    pointer-events: none;
  }

  .coffee-container:hover .coffee-glow {
    opacity: 0.9;
    filter: blur(10px);
    width: 46px;
    height: 46px;
  }

  @keyframes coffee-glow-spin {
    0% {
      transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }

  @keyframes coffee-glow-pulse {
    0% {
      opacity: 0.5;
      transform: translate(-50%, -50%) scale(0.9);
    }
    100% {
      opacity: 0.9;
      transform: translate(-50%, -50%) scale(1.1);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-4px);
    }
  }

  /* 移除原有 .plus-icon 样式，全部由 .plus-icon-glow 控制 */

  .close-icon {
    font-size: 20px;
    color: #606266;
    transition: all 0.3s ease;
  }

  .close-icon:hover {
    color: #409eff;
  }

  .introduction-container {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 300px;
    background-color: #fff;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    padding: 20px;
    z-index: 1000;
    transition: all 0.3s ease;
    transform: translateX(0);
  }

  .introduction-container.collapsed {
    opacity: 0;
    transform: translateX(-100%);
    pointer-events: none;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  .chat-view {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    height: 60px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
    border-bottom: 1px solid #e2e8f0;

    @media (forced-colors: active) {
      background-color: Canvas;
      border-bottom-color: CanvasText;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
    z-index: 2;
    margin-left: auto;
  }

  .connection-status-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .connection-status {
    white-space: nowrap;
  }

  .toggle-button--header {
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    flex-shrink: 0;
    user-select: none;
  }

  .toggle-button--header:hover {
    background-color: #f5f7fa;
  }

  /* 移除了原有的 .plus-icon 样式 */

  .rotate-enter-active,
  .rotate-leave-active {
    transition: transform 0.3s ease;
  }

  .rotate-enter-from,
  .rotate-leave-to {
    transform: rotate(-180deg);
  }

  .introduction-popover {
    margin-top: 8px !important;
    padding: 0;
    border-radius: 8px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
    border: none;
    transition: all 0.2s ease;
    max-width: 320px;
    overflow: hidden;
  }

  .introduction {
    max-height: 60vh;
    overflow-y: auto;
    padding: 16px;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
  }

  .introduction h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
  }

  .introduction p {
    margin: 0 0 12px 0;
    word-break: break-word;
  }

  .support-text,
  .feedback-text {
    margin-top: 16px !important;
    color: #666;
    font-size: 13px;
  }

  .donation-text {
    margin: 12px 0 !important;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.4;
  }

  .wallet-address {
    display: inline-flex;
    align-items: center;
    background: #f1f5f9;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;
    word-break: break-all;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .wallet-address:hover {
    background: #e2e8f0;
  }

  .copy-icon {
    margin-left: 4px;
    color: #64748b;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .clickable-title {
    cursor: pointer;
    text-decoration: none;
  }

  .username-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background-color: #f1f5f9;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    color: #475569;

    @media (forced-colors: active) {
      background-color: ButtonFace;
      border-color: ButtonText;
      color: ButtonText;
    }

    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
  }

  .username-button:hover {
    background-color: #e2e8f0;
    border-color: #cbd5e1;
  }

  .edit-icon {
    font-size: 0.75rem;
    opacity: 0.6;
  }

  .introduction-container {
    max-width: 800px;
    margin: 2rem auto 0;
    padding: 0 1rem;
  }

  .introduction {
    font-style: italic;
    text-align: left;
    margin-bottom: 2rem;
  }

  .introduction h3 {
    font-style: normal;
    margin: 0 0 0.5rem 0;
    color: var(--el-text-color-primary);
  }

  .introduction p {
    margin: 0.5rem 0;
    color: var(--el-text-color-regular);
  }

  .wallet-address {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.2rem 0.5rem;
    background-color: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.9em;
    cursor: pointer;
    transition: all 0.2s;
  }

  .wallet-address:hover {
    background-color: var(--el-color-primary-light-9);
    border-color: var(--el-color-primary-light-5);
  }

  .copy-icon {
    color: var(--el-color-primary);
    font-size: 0.9em;
  }

  .chat-view__setup {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    width: 100%;
  }

  .chat-view__setup .setup-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 500px;
    padding: 0 1rem;
  }

  .chat-view__setup .button-row {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    width: 100%;
    margin-top: 1rem;
  }

  .chat-view__setup h2 {
    color: #1e293b;
    margin-bottom: 1rem;
  }

  .chat-view__loading {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.6rem;
    margin-top: 1rem;
    width: 100%;
  }

  @media (max-width: 768px) {
    .dialog-footer {
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
      width: auto;
    }

    .dialog-footer .el-button {
      width: auto;
      min-width: 100px;
      padding: 0.7rem 1.5rem;
      margin-bottom: 0;
      font-size: 1rem;
    }
  }

  .connection-status {
    padding: 0.5rem;
    text-align: center;
    background: #fff2f0;
    color: #ff4d4f;

    @media (forced-colors: active) {
      color: Mark;
    }

    font-size: 0.875rem;
  }

  .connection-status--connected {
    background: #f6ffed;
    color: #52c41a;

    @media (forced-colors: active) {
      background: Canvas;
      color: MarkText;
    }
  }

  .google-login {
    padding: 0.5rem 1rem;
    background-color: #4285f4;
    color: white;
    border: none;

    @media (forced-colors: active) {
      background-color: ButtonFace;
      color: ButtonText;
      border: 1px solid ButtonText;
    }

    border-radius: 4px;
    cursor: pointer;
  }

  .google-login:hover {
    background-color: #357abd;
  }

  .user-dropdown {
    cursor: pointer;
    margin-left: 16px;
  }

  .user-avatar {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    border-radius: 6px;
    transition: background-color 0.3s;
  }
  
  /* 邀请对话框样式 */
  .invite-dialog {
    .el-dialog__body {
      padding: 20px 20px 10px;
    }
    
    .invite-content {
      text-align: center;
      padding: 10px 0 20px;
      
      p {
        margin: 0 0 15px;
        font-size: 16px;
        color: var(--el-text-color-primary);
      }
    }
    
    .countdown-progress {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background-color: #f0f0f0;
      overflow: hidden;
      border-radius: 0 0 4px 4px;
      
      .progress-bar {
        height: 100%;
        transition: width 1s linear, background-color 1s linear;
      }
    }
    
    .dialog-footer {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-top: 20px;
      
      .el-button {
        min-width: 100px;
      }
    }
  }

  .user-avatar:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .user-name {
    font-size: 14px;
    color: #333;
    margin: 0 4px;
  }

  .el-dropdown-menu {
    padding: 4px 0;
  }

  .el-dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
  }

  .el-icon {
    font-size: 16px;
  }

  .user-avatar-icon {
    background-color: #409eff;
    color: white;

    @media (forced-colors: active) {
      background-color: ButtonFace;
      color: ButtonText;
    }

    font-weight: 500;
  }
</style>
