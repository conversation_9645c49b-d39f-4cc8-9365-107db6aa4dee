/* 
Oauth:
google
github

方案:
BFF + 共享 <PERSON><PERSON> (强烈推荐): 这是最安全、最方便的解决方案。 使用 BFF 处理 OAuth 流程，并使用 HttpOnly 和 Secure Cookie 存储访问令牌。 
所有 SPA 实例都可以自动使用相同的登录状态。
*/

import { cliErr__errPayload, ErrorCode, unknown__cliErr, type User } from "@unibabble/shared"
import UserService from "./UserService"


const MODULE_NAME = 'Client:Services:AuthService'

let googleToken = ''

const GoogleAuth = {

    googleToken: '',

    getToken: () => ({ token: googleToken }),

    cleanToken: () => { googleToken = '' },

    /**
     * Login using Google OAuth credentials
     * @param credential - JWT credential string returned by Google OAuth
     * @returns Parsed user information object containing id, name, lang, email fields
     * @throws If credential parsing fails, throws an exception with error information
      */
    login: (credential: string): User => {
        const method = 'GoogleAuth.Login'
        try {
            googleToken = credential
            const payload = JSON.parse(atob(credential.split('.')[1]))
            return {
                id: payload.sub,
                name: payload.name || 'User',
                lang: UserService.getDefaultUserLang(),
                email: payload.email || ''
            }
        } catch (err) {
            const cliErr = unknown__cliErr(err, ErrorCode.LOGIN_GOOGLE_AUTH_FAILED, method, MODULE_NAME, 'Failed to handle Google login')
            throw cliErr__errPayload(cliErr)
        }
    }
}

export { GoogleAuth }