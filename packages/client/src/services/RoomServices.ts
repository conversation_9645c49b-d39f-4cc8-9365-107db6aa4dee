
import { useChatStore } from '@/stores/useChatStore';
import { createCliErr, ErrorCode, cliErr__errPayload, unknown__cliErr } from '@unibabble/shared';
import { api_leaveRoom, api_isInRoom } from '@/apis';

const MODULE_NAME = 'Client:ApiServices'

export async function apiLeaveRoom() {
    const method = 'apiLeaveRoom'
    const chatStore = useChatStore();

    try {
        if (!chatStore.currentRoom) {
            throw createCliErr(
                ErrorCode.ROOM_NOT_FOUND,
                'Current Room is NULL when leaving room',
                'apiLeaveRoom',
                MODULE_NAME,
                undefined,
                chatStore.currentUser?.id,
            )
        };

        if (!chatStore.currentUser) {
            throw createCliErr(
                ErrorCode.USER_NOT_EXISTS,
                'Current User is NOT exist when leaving room',
                'apiLeaveRoom',
                MODULE_NAME,
                chatStore.currentRoom?.id,
            )
        };

        const res = await api_leaveRoom(chatStore.currentUser.id, chatStore.currentRoom.id);
        if (res && res.code !== ErrorCode.OK) {
            chatStore.error = cliErr__errPayload(unknown__cliErr(res.data), method, MODULE_NAME)
        }

        return res.code
    } catch (err) {
        chatStore.error = cliErr__errPayload(unknown__cliErr(err), method, MODULE_NAME)
        return null
    }
}

export async function apiIsInRoom() {
    const method = 'apiIsInRoom'
    const chatStore = useChatStore();

    try {
        if (!chatStore.currentRoom) {
            throw createCliErr(
                ErrorCode.ROOM_NOT_FOUND,
                'Current Room is NULL when check isInRoom',
                method,
                MODULE_NAME,
                undefined,
                chatStore.currentUser?.id,
            )
        };
        if (!chatStore.currentUser) {
            throw createCliErr(
                ErrorCode.USER_NOT_EXISTS,
                'Current User is NOT exist when check isInRoom',
                method,
                MODULE_NAME,
                chatStore.currentRoom?.id,
            )
        };

        const res = await api_isInRoom(chatStore.currentUser.id, chatStore.currentRoom.id);
        if (res && res.code !== ErrorCode.OK) {
            chatStore.error = cliErr__errPayload(unknown__cliErr(res.data), method, MODULE_NAME)
        }

        return res.code
    } catch (err) {
        chatStore.error = cliErr__errPayload(unknown__cliErr(err), method, MODULE_NAME)
        return null
    }
}

