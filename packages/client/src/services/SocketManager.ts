import { io } from 'socket.io-client';
import type { ISocketManager, ISocketOptions, ClientSocket } from '../types/socket';
import { WebSocketEvents } from '@unibabble/shared';

export class SocketManager implements ISocketManager {
    private static instance: SocketManager | null = null;
    private socket: ClientSocket | null = null;
    private listeners: Map<string, ((...args: any[]) => void)[]> = new Map();
    private options: ISocketOptions;
    private pingInterval: NodeJS.Timeout | null = null;

    private constructor(options: ISocketOptions) {
        this.options = {
            autoConnect: false,
            reconnection: true,
            reconnectionAttempts: 5,
            timeout: 10000,
            ...options
        };
    }

    public static getInstance(options: ISocketOptions): SocketManager {
        if (!SocketManager.instance) {
            SocketManager.instance = new SocketManager(options);
        }
        return SocketManager.instance;
    }

    public async connect(auth?: any): Promise<boolean> {
        if (this.socket?.connected) {
            return true;
        }

        return new Promise((resolve, reject) => {
            try {
                this.socket = io(this.options.url, {
                    autoConnect: this.options.autoConnect,
                    reconnection: this.options.reconnection,
                    reconnectionAttempts: this.options.reconnectionAttempts,
                    timeout: this.options.timeout,
                    auth
                });

                const connectTimeout = setTimeout(() => {
                    reject(new Error('Connection timeout'));
                }, this.options.timeout);

                this.socket.once('connect', () => {
                    clearTimeout(connectTimeout);
                    this.startPing();
                    resolve(true);
                });

                this.socket.once('connect_error', (error) => {
                    clearTimeout(connectTimeout);
                    reject(error);
                });

                this.socket.connect();
            } catch (error) {
                reject(error);
            }
        });
    }

    public disconnect(): void {
        if (this.pingInterval) {
            clearInterval(this.pingInterval);
            this.pingInterval = null;
        }

        this.listeners.forEach((handlers, event) => {
            handlers.forEach(handler => {
                this.socket?.off(event, handler);
            });
        });
        this.listeners.clear();

        this.socket?.disconnect();
        this.socket = null;
    }

    public emit(event: string, data: any): void {
        if (!this.socket?.connected) {
            throw new Error('Socket not connected');
        }
        this.socket.emit(event, data);
    }

    public on(event: string, handler: (...args: any[]) => void): void {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event)?.push(handler);
        this.socket?.on(event, handler);
    }

    public off(event: string, handler: (...args: any[]) => void): void {
        const handlers = this.listeners.get(event);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index !== -1) {
                handlers.splice(index, 1);
                this.socket?.off(event, handler);
            }
            if (handlers.length === 0) {
                this.listeners.delete(event);
            }
        }
    }

    public isConnected(): boolean {
        return this.socket?.connected ?? false;
    }

    private startPing(): void {
        if (this.pingInterval) {
            clearInterval(this.pingInterval);
        }

        this.pingInterval = setInterval(() => {
            const start = Date.now();
            this.socket?.emit(WebSocketEvents.STANDARD.CLIENT.PING, () => {
                const latency = Date.now() - start;
                this.socket?.emit('client:latency', latency);
            });
        }, 5000);
    }
} 