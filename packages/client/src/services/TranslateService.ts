import { api_translate } from "@/apis";
import type { DeepLPostBody, ErrorPayload, LanguageCode, LanguageSource, UserLang } from "@unibabble/shared";


const translate = async (senderLangCode: LanguageSource, receiverLangCode: LanguageCode, content: string): Promise<string | ErrorPayload> => {
    const body: DeepLPostBody = {
        senderLangCode,
        receiverLangCode,
        content
    }
    return await api_translate(body)
};


export { translate };
