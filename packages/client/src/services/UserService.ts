import { uuid, type UserLang, UserLangUtils } from "@unibabble/shared";
import { random } from "lodash";

let userName: string;

const generateRandomUsername = () => {
  const adjectives = ['快乐', '智慧', '友善', '勇敢', '温暖'];
  const nouns = ['旅行者', '探索者', '思考者', '梦想家', '创造者', '干饭人'];
  const randomNum = Math.floor(Math.random() * 1000);

  const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];

  return `${randomAdjective}${randomNoun}${randomNum}`;
}


const userNameKey = 'Uni_UserName'
const getUsername = () => {
  let userName = localStorage.getItem(userNameKey);
  if (!userName) {
    userName = generateRandomUsername();
    localStorage.setItem(userNameKey, userName)
  }
  return userName;
}

const setUsername = (uname: string) => {
  if (isValidUsername(uname)) {
    userName = uname;
    localStorage.setItem(userNameKey, userName)
  }
}

const userIdKey = 'Uni_UserId'
const getUserId = () => {
  let userId = localStorage.getItem(userIdKey);
  if (!userId) {
    userId = uuid();
    localStorage.setItem(userIdKey, userId)
  }
  return userId;
}

/* TODO: 页面添加用户名限制 */
const isValidUsername = (username: string): boolean => {
  return username.length >= 2 && username.length <= 20;
}

// 原始版本，保留作为注释参考
// const defaultUserLang: UserLang = {
//   target: 'EN-US',
//   source: 'EN',
//   name: 'English (American)'
// }

// 获取默认用户语言设置（非压缩格式）
const getDefaultUserLang = (): UserLang => {
  return UserLangUtils.expand(UserLangUtils.createDefault());
}

const userEmailKey = 'Uni_UserEmail'
const userEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>']

const getUserEmail = () => {
  let userEmail = localStorage.getItem(userEmailKey);
  if (!userEmail) {
    userEmail = userEmails[random(userEmails.length - 1, false)];
    localStorage.setItem(userEmailKey, userEmail)
  }
  return userEmail;
}


export default { getDefaultUserLang, getUsername, setUsername, getUserId, getUserEmail }
