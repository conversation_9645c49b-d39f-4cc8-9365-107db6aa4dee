import { render, type RenderOptions } from '@testing-library/vue';
import { createPinia } from 'pinia';
import { createI18n } from 'vue-i18n';
import { createRouter, createWebHistory } from 'vue-router';
import {routes} from '@/router';
import ElementPlus from 'element-plus';
import type { Component } from 'vue';

/**
 * 自定义渲染函数
 * @param component 要渲染的组件
 * @param options 渲染选项
 * @returns 渲染结果和工具函数
 */
export function renderWithPlugins<T = any>(
  component: Component,
  options: RenderOptions<T> = {},
) {
  const pinia = createPinia();
  const router = createRouter({
    history: createWebHistory(),
    routes,
  });

  const i18n = createI18n({
    legacy: false,
    locale: 'zh-CN',
    fallbackLocale: 'en',
    messages: {
      'zh-CN': {},
    },
  });

  return render(component, {
    ...options,
    global: {
      plugins: [
        pinia,
        router,
        i18n,
        ElementPlus,
        ...(options.global?.plugins || []),
      ],
      ...options.global,
    },
  });
}

// 重新导出常用的测试工具
export * from '@testing-library/vue';
export { default as userEvent } from '@testing-library/user-event';
export { renderWithPlugins as render };
