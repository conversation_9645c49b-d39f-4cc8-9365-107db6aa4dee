import { http, HttpResponse, delay } from 'msw';
import type { HttpHandler } from 'msw';

interface User {
  id: string;
  name: string;
  email: string;
}

interface ErrorResponse {
  message: string;
}

export const handlers: HttpHandler[] = [
  // 示例 API 请求处理
  http.get('/api/user', async () => {
    await delay(150); // 模拟网络延迟
    return HttpResponse.json<User>({
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
    });
  }),
  // 在这里添加更多的请求处理
];

export const errorHandlers: HttpHandler[] = [
  http.get('/api/error', () => {
    return HttpResponse.json<ErrorResponse>(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }),
];

// WebSocket 模拟将在后续添加
