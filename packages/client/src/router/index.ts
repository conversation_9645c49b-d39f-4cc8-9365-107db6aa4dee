import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    name: 'HomeView',
    component: () => import('@/views/HomeView.vue') ,
    meta: {
      title: 'UniBabble',
    },
  },
  {
    path: '/room/:roomId',
    name: 'ChatRoom',
    component: () => import('@/views/HomeView.vue') ,
    meta: {
      title: '聊天室 - UniBabble',
    },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到 - UniBabble',
    },
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

// 全局路由守卫：更新页面标题
router.beforeEach((to, from, next) => {
  document.title = (to.meta.title as string) || 'UniBabble';
  next();
});

export default router;
export {routes};
