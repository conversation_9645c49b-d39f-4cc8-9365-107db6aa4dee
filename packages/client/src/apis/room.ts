import { postBackend } from '@/utils'
import type { ApiResponse, RoomUpdatedPayload } from '@unibabble/shared';
import { getTimestamp, logger } from '@unibabble/shared';

export const api_leaveRoom = (userId: string, roomId: number): Promise<ApiResponse<RoomUpdatedPayload | undefined | unknown>> => {
    const rep = postBackend('/room/leave', { userId, roomId, timestamp: getTimestamp() })
    logger.debug('api_leaveRoom', {
        module: 'Client:api',
        timestamp: getTimestamp(),
        userId,
        roomId,
        details: { rep }
    })
    return rep
}

export const api_isInRoom = (userId: string, roomId: string): Promise<ApiResponse<undefined | unknown>> => {
    return postBackend('/room/isin', { userId, roomId, timestamp: getTimestamp() })
}

