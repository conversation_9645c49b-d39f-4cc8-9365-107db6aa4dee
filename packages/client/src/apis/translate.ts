import type {
    DeepLPostBody, ApiResponse, TranslateLang, ErrorPayload,
    TranslateResult
} from '@unibabble/shared';
import {
    logger, getTimestamp, ErrorCode, createCliErr,
    cliErr__errPayload, unknown__cliErr, UnibabbleError
} from '@unibabble/shared';
import { getBackend, postBackend } from '@/utils/http';


const MODULE_NAME = 'Client:apis:translate'

// export const api_checkLangs = async (): Promise<TranslateLang[] | ErrorPayload> => {

//     const method = 'api_checkLangs'

//     try {
//         const rep: ApiResponse<TranslateLang[]> = await getBackend<TranslateLang[]>('/translate/langs')

//         logger.info('Get langs from server', {
//             module: MODULE_NAME,
//             timestamp: getTimestamp(),
//             details: { rep }
//         })

//         if (rep.code !== ErrorCode.OK) {
//             throw createCliErr(
//                 rep.code,
//                 'Failed to check langs',
//                 method,
//                 MODULE_NAME,
//                 undefined,
//                 undefined,
//                 undefined,
//                 { ...rep }
//             )
//         } else if (!rep) {
//             throw createCliErr(
//                 ErrorCode.API_RESPONSE_ERROR,
//                 'API response is undefined',
//                 method,
//                 MODULE_NAME,
//                 undefined,
//                 undefined,
//                 undefined,
//                 { rep }
//             )
//         } else if (!rep.data) {
//             throw createCliErr(
//                 ErrorCode.API_RESPONSE_NO_DATA,
//                 'API response no data',
//                 method,
//                 MODULE_NAME,
//                 undefined,
//                 undefined,
//                 undefined,
//                 { ...rep }
//             )
//         } else if (rep.data instanceof UnibabbleError) {
//             throw rep.data
//         }

//         return rep.data
//     } catch (e) {
//         return cliErr__errPayload(unknown__cliErr(e), method, MODULE_NAME)
//     }
// }


export const api_translate = async (body: DeepLPostBody): Promise<string | ErrorPayload> => {
    const method = 'api_translate'

    try {
        const rep = await postBackend<
            DeepLPostBody,
            TranslateResult
        >('/translate', body)

        logger.info('Get translation result from server', {
            module: MODULE_NAME,
            timestamp: getTimestamp(),
            details: { rep }
        })

        if (!rep.translated) {
            throw createCliErr(
                ErrorCode.API_DEEPL_RESPONSE_NO_CONTENT,
                'No translations returned from DeepL',
                method,
                MODULE_NAME,
                undefined,
                undefined,
                undefined,
                { rep }
            )
        }

        return rep.translated
    } catch (e) {
        return cliErr__errPayload(unknown__cliErr(e), method, MODULE_NAME)
    }

}