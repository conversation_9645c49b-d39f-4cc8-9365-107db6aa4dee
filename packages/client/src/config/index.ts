import type { 
  AppConfig, BaseServiceConfig, ChatConfig, ClientConnectionConfig, 
  ClientServiceConfig, LogConfig, OAuthConfig 
} from '@unibabble/shared';

const envMode = import.meta.env.MODE;
console.log('当前环境:', envMode);


export const cliConfig = {
  // common
  env: import.meta.env.VITE_ENV || 'dev',
  auth: {
    enable: import.meta.env.VITE_AUTH_ENABLED?.toLowerCase() === 'true',   // 默认导入的类型是string, 即便env.d.ts中约束为boolean, 改为 === 'true' 才能正确解析
    google: {
      clientId: import.meta.env.VITE_APP_GOOGLE_CLIENT_ID || ''
    }
  } as OAuthConfig,
  log: {
    level: import.meta.env.VITE_LOG_LEVEL || 'INFO'
  } as LogConfig,
  server: {
    host: import.meta.env.VITE_SERVER_HOST! || 'localhost',
    port: import.meta.env.VITE_SERVER_PORT || 3000,
    ws: import.meta.env.VITE_WS_PATH || '/unibabble.io',
    api: import.meta.env.VITE_API_PATH || '/api',
  } as BaseServiceConfig,
  chat: {
    perInviteTimeout: import.meta.env.VITE_CHAT_PER_INVITE_TIMEOUT || 13000,
    randomInviteCount: import.meta.env.VITE_CHAT_RANDOM_INVITE_COUNT || 5
  } as ChatConfig,

  // client
  client: {
    host: import.meta.env.VITE_CLIENT_HOST || 'localhost',
    port: import.meta.env.VITE_CLIENT_PORT || 5173,
    secureProtocol: import.meta.env.VITE_SECURE_PROTOCOL?.toLowerCase() === 'true'
  } as ClientServiceConfig,

  connection: {
    reconnectionAttempts: import.meta.env.VITE_RECONNECT_ATTEMPTS || 5,
    reconnectionDelay: import.meta.env.VITE_RECONNECT_DELAY || 1000,
    reconnectionDelayMax: import.meta.env.VITE_RECONNECT_DELAY_MAX || 5000,
    connectTimeout: import.meta.env.VITE_CONNECT_TIMEOUT || 5000,
    ackTimeout: import.meta.env.VITE_ACK_TIMEOUT || 20000
  } as ClientConnectionConfig,
  app: {
    title: import.meta.env.VITE_APP_TITLE || 'UniBabble',
    version: import.meta.env.VITE_APP_VERSION || '0.0.1',
    description: import.meta.env.VITE_APP_DESCRIPTION || 'Real-time multilingual chat application',
    url: import.meta.env.VITE_APP_URL
  } as AppConfig
}
