import type { Socket } from 'socket.io-client';
import type {
    SystemPayload,
    ErrorPayload,
    Message,
    Room,
    User,
    SendMessagePayload,
    TextMessageWithState,
    JoinRoomPayload,
    LeaveRoomPayload,
    RoomUpdatedPayload,
    MessageDeliveredPayload,
    HistorySharePayload,
    LanguageCode,
    ServerEmitEvents,
    LanguageSource,
    KickUserPayload,
    UserKickedPayload,
    UserJoinedPayload,
    ClientSocket,
    ClientListenEvents,
    ClientEmitEvents,
    WebSocketEvents,
    UnibabbleWebSocketEventMap
} from '@unibabble/shared';

export interface ISocketState {
    isConnected: boolean;
    isConnecting: boolean;
    error: ErrorPayload | null;
    latencies: number[];
}

export type SocketEventHandler<T> = (payload: T) => void;

// 监听器类型
export type SocketListener<E extends keyof ClientListenEvents> = {
    event: E;
    handler: ClientListenEvents[E];
};

// Socket管理器接口
export interface ISocketManager {
    socket: ClientSocket | null;
    isConnected: boolean;
    isConnecting: boolean;

    // 基础方法
    connect(): Promise<boolean>;
    disconnect(): void;

    // 类型安全的事件处理方法
    on<E extends keyof ClientListenEvents>(event: E, handler: ClientListenEvents[E]): void;
    off<E extends keyof ClientListenEvents>(event: E, handler: ClientListenEvents[E]): void;
    emit<E extends keyof ClientEmitEvents>(event: E, payload: Parameters<ClientEmitEvents[E]>[0]): void;

    // 清理方法
    clearListeners(): void;
}

export interface ISocketOptions {
    url: string;
    autoConnect?: boolean;
    reconnection?: boolean;
    reconnectionAttempts?: number;
    timeout?: number;
}

export interface IChatState {
    socket: ISocketState;
    user: {
        currentUser: User | null;
        currentRoom: Room | null;
    };
    chat: {
        messages: Map<string, Message>;
        enableTranslate: boolean;
        selectedTargetLangCode: LanguageCode;
        selectedSourceLangCode: LanguageSource;
    };
}

// export type ClientSocket = Socket; 
