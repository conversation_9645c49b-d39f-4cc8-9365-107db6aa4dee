<template>
  <div class="language-selector">
    <el-select
      v-model="selectedLanguageCode"
      size="default"
    >
      <el-option
        v-for="i in AvailableLanguages"
        :key="i[0]"
        :value="i[0]"
        :label="i[1]"
      />
    </el-select>
  </div>
</template>

<script name="LanguageSelector" setup lang="ts">
import { useChatStore } from '@/stores';
import { AvailableLanguages } from '@unibabble/shared'
import type { LanguageCode, LanguageSource } from '@unibabble/shared'

const chatStore = useChatStore();

const selectedLanguageCode = computed({
  get: () => chatStore.selectedTargetLangCode,
  set: (code: LanguageCode) => {
    chatStore.selectedTargetLangCode = code
  }
});

watch(selectedLanguageCode, (newCode: LanguageCode) => {
  if (!newCode) return  
  chatStore.selectedSourceLangCode = newCode.slice(0, 2) as LanguageSource
})

</script>

<style scoped lang="scss">
.language-selector {
  min-width: 120px;
}
</style>
