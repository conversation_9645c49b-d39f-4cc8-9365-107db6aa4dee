<template>
  <div class="chat-room-container">
    <!-- 加载状态 (全屏覆盖或在内容区域居中) -->
    <div v-if="!roomInfo || !chatStore.currentUser" class="loading-overlay">
      <el-icon class="is-loading"><el-loading /></el-icon>
      <p>Loading Room Data...</p>
    </div>

    <!-- 聊天室主内容 (当数据加载完成后显示) -->
    <div v-else class="chat-room-layout">
      <!-- Header (固定在顶部) -->
      <header class="chat-header">
        <div class="header-left">
          <!-- 房间信息 -->
          <div class="header-content">
            <div class="room-title">
              <h2>Room: {{ chatStore.currentRoom?.id }}</h2>
              <el-tooltip content="Copy Room Link">
                <el-icon class="copy-icon" @click="copyRoomLink"><Files /></el-icon>
              </el-tooltip>
            </div>
            <!-- 用户状态/语言 -->
            <div class="user-status">
              <el-tag v-for="user in roomInfo.users" :key="user.id"
                    :type="user.id === chatStore.currentUser?.id ? 'primary' : 'info'" size="small" class="user-tag">
                    {{ getUserName(user.id) }} ({{ user.lang.source }})
                </el-tag>
            </div>
          </div>
        </div>

        <div class="header-right">
          <!-- 连接状态 -->
          <!-- <div :class="['connection-badge', { 'is-connected': isConnected }]">
            {{ connectionStatus }}
          </div> -->

          <!-- 更多操作下拉菜单 -->
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              <el-icon><MoreFilled /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="isAdmin" @click="openKickDialog">Kick User</el-dropdown-item>
                <el-dropdown-item @click="downloadChats">Download Dialog</el-dropdown-item>
                <!-- 分享功能入口 (如果启用) -->
                <!-- <el-dropdown-item @click="openShareDialog">分享聊天记录</el-dropdown-item> -->

                <!-- 翻译开关可以放在这里，或者考虑更独立的设置面板 -->
                 <el-dropdown-item divided>
                     <div class="flex items-center justify-between w-full">
                       <el-switch v-model="isEnableTrans" size="small" />
                       <span> Translate Enable </span>
                     </div>
                 </el-dropdown-item>
                 <el-dropdown-item>
                     <div class="flex items-center justify-between w-full">
                       <el-switch v-model="isShowOriginalContent" size="small" />
                       <span> Show original Content </span>
                     </div>
                 </el-dropdown-item>

                <el-dropdown-item divided @click="leaveRoom">
                   <el-icon><SwitchButton /></el-icon> Leave Room
                </el-dropdown-item>
                <el-dropdown-item v-if="!isConnected" @click="reenterRoom">
                   <el-icon><Refresh /></el-icon> Re-Enter
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </header>

      <!-- 消息列表区域 -->
      <div class="message-area">
         <!-- 错误消息提示 (可以考虑更醒目的位置或样式) -->
         <el-alert
            v-if="error"
            :title="error.errMsg?.content || 'Error Occur'"
            type="error"
            show-icon
            closable
            @close="clearError"
            class="chat-error-alert"
         />
        <MessageList
          class="message-list-container"
          :messages="sortedMessages"
          :me="chatStore.currentUser"
          :loading="isConnecting"
          :is-enable-trans="isEnableTrans"
          :is-show-original-content="isShowOriginalContent"
          @resend="handleSend"
        />
      </div>

      <!-- 消息输入区域 (固定在底部) -->
      <div class="input-area">
        <MessageInput
          v-model="inputMessage"
          :disabled="!isConnected"
          :placeholder="isConnected ? 'Message Input...' : 'unconnected'"
          @send="handleSend"
        />
      </div>
    </div>

    <!-- 对话框 (踢出用户，分享等) -->
    <el-dialog v-model="kickDialogVisible" title="Kick User" width="30%">
       <!-- 对话框内容与逻辑保持不变 -->
       <el-checkbox-group v-model="selectedKickUsers">
          <el-checkbox v-for="user in roomUsers" :key="user.id" :label="user.id"
             :disabled="user.id === chatStore.currentUser?.id">
             {{ getUserName(user.id) }}
          </el-checkbox>
       </el-checkbox-group>
       <template #footer>
          <span class="dialog-footer">
             <el-button @click="kickDialogVisible = false">Cancel</el-button>
             <el-button type="danger" @click="handleKick">Confirm</el-button>
          </span>
       </template>
    </el-dialog>

    <!-- 分享对话框 (如果启用) -->
    <!-- <el-dialog v-model="shareDialogVisible" title="分享聊天记录" width="30%">
       ...
    </el-dialog> -->

  </div>
</template>

<script setup name="ChatRoom" lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, onUnmounted } from 'vue'; // Ensure Vue Composition API functions are imported
import { logger, getTimestamp, ErrorCode, EMessageType, createCliErr } from '@unibabble/shared';
import type { TextMessageWithState } from '@unibabble/shared'; // Import necessary types
import { MessageInput, MessageList } from '@/components';
import { catchErr } from '@/utils';
import { useChatStore } from '@/stores';
import { cliConfig } from '@/config';
import { ElMessageBox, ElMessage, ElTooltip } from 'element-plus';
import { Refresh, Files, MoreFilled, SwitchButton } from '@element-plus/icons-vue'; // Import new icons and Loading icon

const MODULE_NAME = 'ChatRoom';

const chatStore = useChatStore();

const props = defineProps(['urlRoomId']);

const emit = defineEmits<{
  (e: 'error', message: string, module: string, userId?: string): void,
  (e: 'reenter', cleanRoomWhenJoinFailed?: boolean, isReenter?: boolean, errStr?: string, moduleName?: string): void,
  (e: 'update:isMounted', flag: boolean): void,
  (e: 'update:urlRoomId', roomId: string): void,
}>();

// const emitErr = (err: unknown, desc?: string) => emit('error', checkErrMsg(err, desc), MODULE_NAME, chatStore.currentUser?.id);
const emitReenter = (cleanRoomWhenJoinFailed?: boolean, isReenter?: boolean, errStr?: string, moduleName?: string) => emit('reenter', cleanRoomWhenJoinFailed, isReenter, errStr, moduleName);
const emitMountFlag = (flag: boolean) => emit('update:isMounted', flag);
const cleanUrlRoomId = () => emit('update:urlRoomId', '');

const needLeave = computed({
  set: (v: boolean) => chatStore.needLeave = v,
  get: () => chatStore.needLeave
});
const inputMessage = ref('');
const isConnected = computed(() => chatStore.isConnected);
const isConnecting = computed(() => chatStore.isConnecting);
const isShowOriginalContent = ref(true);

// computed
const sortedMessages = computed(() =>
  Array
    .from(chatStore.messages.values())
    .sort((a, b) => a.timestamp - b.timestamp)
);

const isEnableTrans = computed({
  get: () => chatStore.enableTranslate,
  set: (value) => {
    chatStore.enableTranslate = value
  }
})

const roomInfo = computed(() => {
  const contains = chatStore.currentRoom?.users.filter((user) => user.id === chatStore.currentUser?.id)
  logger.debug('chatStore.currentRoom.users contains me? ', {
    module: MODULE_NAME,
    timestamp: getTimestamp()
  })

  if (contains && chatStore.currentRoom) return chatStore.currentRoom;
  return null;
});

const error = computed(() => chatStore.error);

const isAdmin = computed(() => chatStore.currentRoom?.adminId === chatStore.currentUser?.id);

// const connectionStatus = computed(() => {
//   if (isConnected.value) return '已连接';
//   if (isConnecting.value) return '连接中...';
//   return '未连接';
// });

// funcs
const getUserName = (userId: string): string => {
  if (userId === chatStore.currentUser?.id) {
    return 'Me';
  }

   // Find the user in the roomInfo.users array to get their name
   const user = roomInfo.value?.users.find(u => u.id === userId);
   return user?.name || userId; // Fallback to user ID if name is not available
}

const leaveRoom = async () => {
  try {
    await ElMessageBox.confirm(
      'Leave Room now？',
      'Warning',
      {
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
    );
    // 用户确认后执行离开房间的逻辑 
    cleanUrlRoomId();
    await chatStore.leaveRoom();
    // The actual navigation to home should be handled by the parent component (HomeView)
    // reacting to the 'update:urlRoomId' emit.
  } catch (err) {
    // User cancelled the operation
    console.log('User cancelled leaving the room');
  }
};

/* 服务端消息计数 : 只要有一个ack的消息就算一条*/
const handleSend = async (content: string, resend?: TextMessageWithState) => {
  try {
    // 验证 content
    const messageContent = resend ? resend.content : content.trim();
    if (!messageContent) return;

    if (messageContent.length > 1000) {
      throw createCliErr(
        ErrorCode.MESSAGE_TOO_LONG,
        'Message content exceeds maximum length',
        'handleSend',
        MODULE_NAME,
        roomInfo.value?.id,
        chatStore.currentUser?.id,
        undefined,
        {
          content: messageContent,
          resend: resend ? resend : undefined
        }
      );
    }

    // 过滤非法字符
    const sanitizedContent = messageContent.replace(/<[^>]*>?/gm, '');

    logger.debug(resend ? 'Resending message' : 'Sending message', {
      module: MODULE_NAME,
      method: 'handleSend',
      timestamp: Date.now(),
      userId: chatStore.currentUser?.id,
      roomId: chatStore.currentRoom?.id,
      details: {
        messageLength: sanitizedContent.length,
        language: chatStore.currentUser?.lang,
        userName: chatStore.currentUser?.name,
        resend: resend
      }
    });

    if (!chatStore.currentRoom) {
      throw createCliErr(
        ErrorCode.ROOM_NOT_EXISTS,
        'Current room is NOT EXIST',
        'handleSend',
        MODULE_NAME,
        roomInfo.value?.id,
        chatStore.currentUser?.id,
        undefined,
        {
          content: sanitizedContent,
          resend: resend ? resend : undefined
        }
      );
    }

    await chatStore.sendMessage(sanitizedContent, chatStore.currentRoom!.id, resend);
    if (!resend) {
       inputMessage.value = ''; // Only clear input for new messages, not resends
    }
  } catch (err) {
    chatStore.error = catchErr(err, 'handleSend', MODULE_NAME);
  }
};

const reenterRoom = async () => {
  emitReenter(false, true, `Reenter Room: ${props.urlRoomId} Failed`, MODULE_NAME);
}

const clearError = () => {
  logger.debug('Clearing error', {
    module: MODULE_NAME,
    method: 'clearError',
    timestamp: Date.now(),
    userId: chatStore.currentUser?.id,
    roomId: chatStore.currentRoom?.id,
    details: {
      language: chatStore.currentUser?.lang
    }
  });
  chatStore.error = null;
};

const copyIcon = ref('📋'); // State variable for the copy icon (though the template now uses ElIcon)
const copyRoomLink = async () => {
  const roomId = chatStore.currentRoom?.id;
  if (roomId) {
    try {
      console.log('config.app.url:', cliConfig.app.url);
      const url = `${cliConfig.app.url}/room/${roomId}`;
      await navigator.clipboard.writeText(url);

      // copyIcon.value = '✅'; // No longer needed if using ElMessage/Tooltip feedback
      ElMessage.success('Copied');
      // setTimeout(() => { copyIcon.value = '📋'; }, 3000); // No longer needed
    } catch (error) {
      console.error('Failed to copy:', error);
      ElMessage.error('Copy Failed，Please manual');
    }
  }
};

const downloadChats = () => {
  // 过滤消息列表，排除系统和错误消息
  const filteredMessages = sortedMessages.value.filter(
    (message) => message.type !== EMessageType.SYSTEM && message.type !== EMessageType.ERROR
  );

  // 将消息列表转换为JSON格式
  const jsonContent = JSON.stringify(filteredMessages, null, 2);

  // 创建Blob对象
  const blob = new Blob([jsonContent], { type: 'application/json' });

  // 创建下载链接
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `chat_history_${new Date().toISOString()}.json`;

  // 触发下载
  link.click();

  // 释放URL对象
  URL.revokeObjectURL(url);
};

const shareDialogVisible = ref(false);
const selectedUsers = ref<string[]>([]);
const roomUsers = computed(() => chatStore.currentRoom?.users || []);
const openShareDialog = () => {
  // selectedUsers.value = []; // 清空已选用户
  // shareDialogVisible.value = true;
};
const handleShare = () => {
  if (selectedUsers.value.length > 0) {
    const roomId = chatStore.currentRoom?.id;
    if (roomId) {
      selectedUsers.value.forEach((userId) => {
        chatStore.shareHistory(roomId, userId);
      });
    }
  }
  shareDialogVisible.value = false;
};

const kickUser = (kickee: string[]) => {
  chatStore.kickUser(kickee);
};

const kickDialogVisible = ref(false);
const selectedKickUsers = ref<string[]>([]);

const openKickDialog = () => {
  if (!isAdmin.value) {
    ElMessage.warning('只有管理员才能踢出用户');
    return;
  }
  selectedKickUsers.value = [];
  kickDialogVisible.value = true;
};

const handleKick = async () => {
  if (selectedKickUsers.value.length === 0) {
    ElMessage.warning('请选择要踢出的用户');
    return;
  }

  try {
    await ElMessageBox.confirm(
      '确定要踢出选中的用户吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    kickUser(selectedKickUsers.value);
    kickDialogVisible.value = false;
    ElMessage.success('操作成功');
  } catch (err) {
    console.log('用户取消操作');
  }
};

// life cycle
onMounted(() => {
  emitMountFlag(true)
});

onBeforeUnmount(() => {
  if (roomInfo.value) {
    needLeave.value && leaveRoom()
  }
})

onUnmounted(() => {
  needLeave.value && emitMountFlag(false)
  !needLeave.value && (needLeave.value = true)
})

</script>

<style scoped lang="scss">
@use "sass:color";
@use '@/styles/variables' as *; // 引入设计规范变量
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap'); // 示例：引入一个更现代的字体

.chat-room-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: $color-background; // 使用变量
  font-family: 'Roboto', sans-serif; // 使用引入的字体或全局字体栈
}

.loading-overlay {
  position: absolute; // 或 fixed, 根据需要
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba($color-background, 0.8); // 半透明背景
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: $z-index-modal; // 使用变量
  color: $color-text-secondary; // 使用变量
}

.chat-room-layout {
  display: flex;
  flex-direction: column;
  height: 100%; // 占据容器剩余空间
  overflow: hidden; // 防止内部元素溢出导致滚动条出现在这里
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-md $spacing-lg;
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
  position: relative;
  z-index: 10;

  .header-left {
    flex: 1;
    display: flex;
    justify-content: center;

    .header-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: $spacing-sm;

      .room-title {
        display: flex;
        align-items: center;
        gap: $spacing-sm;

        h2 {
          margin: 0;
          font-size: 1.25rem;
        }

        .copy-icon {
          cursor: pointer;
          color: var(--el-color-primary);
          transition: color 0.2s;

          &:hover {
            color: var(--el-color-primary-light-3);
          }
        }
      }

      .user-status {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: $spacing-xs;
        margin-top: 2px;

        .user-tag {
          margin: 0;
        }
      }
    }
  }
}

.header-left,
.header-right {
  display: flex;
  align-items: center;
  gap: $spacing-md; // 使用变量
}

.room-title {
  display: flex;
  align-items: center;
  gap: $spacing-xs; // 使用变量
  h2 {
    font-size: $font-size-lg; // 使用变量
    color: $color-text; // 使用变量
    margin: 0;
  }
  .copy-icon {
    cursor: pointer;
    color: $color-text-light; // 使用变量
    transition: color 0.3s ease;
    &:hover {
      color: $color-primary; // 悬停变主色
    }
  }
}

.user-status {
    display: flex;
    gap: $spacing-xs;
    flex-wrap: wrap; // 防止用户过多时溢出
}

.user-tag {
    // Element Plus Tag Style, 可以根据设计变量覆盖默认颜色
    // Consider adjusting margin-bottom if flex-wrap is active
    margin-bottom: $spacing-xs; // Add some space below tags when they wrap
}


.connection-badge {
  padding: $spacing-xs $spacing-sm; // 使用变量
  border-radius: $border-radius-md; // 使用变量
  font-size: $font-size-sm; // 使用变量
  &.is-connected {
    background-color: $color-success-bg; // 使用变量
    color: $color-success; // 使用变量
  }
  &:not(.is-connected) {
    background-color: $color-error-bg; // 使用变量
    color: $color-error; // 使用变量
  }
}

.el-dropdown-link {
    cursor: pointer;
    font-size: $font-size-lg;
    color: $color-text-secondary;
    display: flex;
    align-items: center;
}

.message-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.chat-error-alert {
    margin: $spacing-sm $spacing-md; // 使用变量
}

.message-list-container {
  flex: 1;
  overflow-y: auto;
  padding: $spacing-sm $spacing-md; // Use variables for padding
}

.input-area {
  flex-shrink: 0;
  padding: $spacing-sm $spacing-md; // Use variables for padding
  background-color: $color-background-light;
  border-top: 1px solid $color-border;
}

// Adjust MessageInput internal Element Plus styles
:deep(.message-input) {
    .el-input {
        .el-input__wrapper {
            border-radius: $border-radius-md; // Use variables
            // Adjust padding inside input if needed
        }
        .el-input-group__append {
             background-color: $color-primary; // Use primary color variable
             color: white;
             border-color: $color-primary; // Consistent border color
             border-radius: 0 $border-radius-md $border-radius-md 0; // Rounded corner on the right
             padding: 0 $spacing-md; // Use spacing variable for padding
             .el-button {
                 color: white !important;
                 &.is-disabled {
                     background-color: color.adjust($color-primary, $lightness: -10%) !important; // Darken color for disabled state
                     border-color: color.adjust($color-primary, $lightness: -10%) !important;
                 }
             }
        }
    }
}

// Dialog styles (kick user, share etc.), keep Element Plus default or adjust as needed
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: $spacing-sm; // Use spacing variable
  margin-top: $spacing-md; // Use spacing variable
}

@media (max-width: $breakpoint-md) { // Use breakpoint variable
  .chat-header {
    flex-direction: column;
    gap: $spacing-sm;
    align-items: flex-start;
  }

  .header-left, .header-right {
      width: 100%;
      justify-content: space-between;
      gap: $spacing-sm;
  }

  .room-title h2 {
      font-size: $font-size-base;
  }

    .user-status {
       width: 100%;
       justify-content: flex-start;
    }

  .input-area {
     padding: $spacing-sm; // Adjust padding for small screens
  }

  .message-list-container {
     padding: $spacing-sm; // Adjust padding for small screens
  }

   // Adjust dialog footer buttons for small screens if necessary (from previous analysis)
   .dialog-footer {
      flex-direction: column;
      align-items: center;
      gap: $spacing-xs;
      width: auto;
   }

   .dialog-footer .el-button {
      width: auto;
      min-width: 100px;
      padding: $spacing-sm $spacing-lg; // Use spacing variables
      margin-bottom: 0;
      font-size: $font-size-base;
   }
}

</style>
