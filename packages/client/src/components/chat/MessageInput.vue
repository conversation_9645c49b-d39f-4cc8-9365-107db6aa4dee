<template>
  <div class="message-input">
    <el-input
      v-model="inputValue"
      type="text"
      :placeholder="placeholder"
      :disabled="disabled || isSending"
      @keyup.enter="handleSend"
    >
      <template #append>
        <el-button
          :loading="isSending"
          :disabled="disabled || isSending || !inputValue.trim()"
          @click="handleSend"
        >
          Send
        </el-button>
      </template>
    </el-input>
  </div>
</template>

<script setup name="MessageInput" lang="ts">

// Props 定义
const props = defineProps<{
  modelValue: string;
  disabled?: boolean;
  placeholder?: string;
}>();

// Emits 定义
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
  (e: 'send', content: string): void;
}>();

// 响应式数据
const inputValue = ref<string>(props.modelValue);
const isSending = ref(false);

watch(() => props.modelValue, (newValue:string) => {
  inputValue.value = newValue;
});

watch(inputValue, (newValue:string) => {
  emit('update:modelValue', newValue);
});

// 方法
async function handleSend() {
  const content = inputValue.value.trim();
  if (!content || props.disabled || isSending.value) return;

  try {
    isSending.value = true;
    emit('send', content);
    // 清空输入
    inputValue.value = '';
    emit('update:modelValue', '');
  } finally {
    isSending.value = false;
  }
}
</script>

<style scoped lang="scss">
  @use '@/styles/variables' as *;

  .message-input {
    padding: $spacing-md;
    border-top: 1px solid $color-border;
    background: $color-background;
  }
</style>
