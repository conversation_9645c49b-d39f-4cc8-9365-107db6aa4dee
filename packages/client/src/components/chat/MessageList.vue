<template>
  <div class="message-list" ref="messageListRef" @scroll="handleScroll">
    <div v-if="loading" class="message-loading">
      <el-loading />
      Loading...
    </div>

    <div v-else-if="!messages.length" class="message-empty">
      <el-empty description="No messages yet" />
    </div>

    <template v-else>
      <div v-for="message in messages" :key="message.id" :class="['message-item', getMessageClass(message)]">
        <!-- 系统消息 -->
        <template v-if="message.type === 1">
          <div class="message-system">
            {{ message.content }}
          </div>
        </template>

        <!-- 错误消息 -->
        <!--
        <template v-else-if="message.type === 2">
          <div class="message-error">
            <el-alert type="error" :title="message.content" />
          </div>
        </template>
        -->

        <!-- 文本消息 -->
        <!--<template v-else-if="message.type !== 2">
          <div class="message-header">
            <div class="sender-info">
              <span class="sender">{{ message.sender.id }}</span>
              <span class="language">[{{ message.sender.lang }}]</span>
            </div>
            <span class="time">{{ formatTime(message.timestamp) }}</span>
          </div>
          <div class="message-content">
            <div class="original-content" v-if="showOriginal(message)">
              {{ message.content }}
            </div>
            <div class="translated-content">
              {{ getMessageContent(message) }}
            </div>
          </div>
        </template>-->


        <template v-else-if="message.type !== 2">
          <div class="message-container">
            <div v-if="isMyMessage(message) && !isDelivered(message)" class="resend-button" @click="emitResend(message)">
              <el-icon><Refresh /></el-icon>
            </div>
            <div class="message-content-wrapper">
              <div class="message-header">
                <div class="sender-info">
                  <span class="sender">{{ message.sender.name }}</span>
                  <span class="language">[{{ message.sender.lang.target }}]</span>
                </div>
                <span class="time">&nbsp;{{ formatTime(message.timestamp) }}</span>
              </div>
              <div class="message-content">
                <div class="original-content" v-if="showOriginal(message)">
                  {{ message.content }}
                </div>
                <div class="translated-content">
                  {{ getMessageContent(message) }}
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </template>

    <!-- 新消息提示 -->
    <div v-if="hasNewMessage && !isAtBottom" class="new-message-alert" @click="scrollToBottom">
      <el-icon>
        <ArrowDown />
      </el-icon>
      New Message
    </div>
  </div>
</template>

<script name="MessageList" setup lang="ts">
import type { Message, User, TextMessageWithState } from '@unibabble/shared';
import { formatTime, EMessageType, EMessageState, getTimestamp } from '@unibabble/shared';
import { ArrowDown, Refresh } from '@element-plus/icons-vue';

interface Props {
  messages: Message[];
  me: User;
  loading: boolean;
  isEnableTrans: boolean;
  isShowOriginalContent: boolean;
}
const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'resend', message: string, resend?: TextMessageWithState): void;
}>();

const emitResend = (resend: TextMessageWithState) => {
  resend.state.state = EMessageState.RESENDING;
  resend.state.stateAt = getTimestamp();
  emit('resend', '', resend)
}

const messageListRef = ref<HTMLElement | null>(null);
const isAtBottom = ref(true);
const hasNewMessage = ref(false);

const isMyMessage = (message: TextMessageWithState) => {
  return message.sender.id === props.me.id
}

const isDelivered = (message: TextMessageWithState) => {
  return message.state.state === EMessageState.DELIVERED
}


// 获取消息样式类
function getMessageClass(message: Message) {
  const classes = {
    'message-item--self': false,
    'message-item--system': false,
    'message-item--error': false
  };

  switch (message.type) {
    case EMessageType.TEXT:
      classes['message-item--self'] = isSelfMessage(message);
      break;
    case EMessageType.SYSTEM:
      classes['message-item--system'] = true;
      break;
    case EMessageType.ERROR:
      classes['message-item--error'] = true;
      break;
  }

  return classes;
}

// 判断是否是自己发送的消息
function isSelfMessage(message: Message): boolean {
  return message.type === EMessageType.TEXT && message.sender.id === props.me.id;
}

// 是否显示原文
function showOriginal(message: Message): boolean {
  return message.type === EMessageType.TEXT
    && !isMyMessage(message)
    && !!message.translatedContent
    && !!props.isShowOriginalContent
    && message.sender.lang !== props.me.lang;
}

// 获取消息内容
function getMessageContent(message: Message): string {
  /* 如果sender的语种与自己相同, 不翻译, 取content

     如果sender的语种与自己不同:
        isEnableTrans:
          true, 获取翻译结果
          false, 不翻译, 取content
        isShowOriginalContent:
          true, 取content
          false, 取翻译结果
  */

  if (message.type !== EMessageType.TEXT) return message.content;

  if (props.isEnableTrans && !isMyMessage(message as TextMessageWithState)) {
    return message.translatedContent || message.content;
  }

  return message.content;
}

// 检查是否在底部
function checkIsAtBottom(): boolean {
  if (!messageListRef.value) return true;
  const { scrollTop, scrollHeight, clientHeight } = messageListRef.value;
  return Math.abs(scrollHeight - scrollTop - clientHeight) < 1;
}

// 滚动处理
function handleScroll() {
  isAtBottom.value = checkIsAtBottom();
  if (isAtBottom.value) {
    hasNewMessage.value = false;
  }
}

// 滚动到底部
function scrollToBottom() {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight;
    hasNewMessage.value = false;
  }
}

// 监听消息变化
watch(() => props.messages, (newMessages: Message[], oldMessages: Message[] | undefined) => {
  if (!oldMessages || newMessages.length > oldMessages.length) {
    if (isAtBottom.value) {
      nextTick(scrollToBottom);
    } else {
      hasNewMessage.value = true;
    }
  }
}, { deep: true });

// life cycle
onMounted(() => {
  scrollToBottom();
});

onUpdated(() => {
  if (isAtBottom.value) {
    scrollToBottom();
  }
});
</script>

<style scoped lang="scss">
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: relative;
}

.message-loading,
.message-empty {
  text-align: center;
  color: #64748b;
  padding: 2rem;
}

.message-item {
  max-width: 80%;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  background: #f0f2f5;
  align-self: flex-start;

  &--self {
    background: #e6f4ff;
    align-self: flex-end;
  }

  &--system {
    max-width: none;
    align-self: center;
    background: transparent;
    color: #64748b;
    font-size: 0.875rem;
  }

  &--error {
    max-width: none;
    align-self: center;
    background: transparent;
  }
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.sender-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sender {
  font-weight: 500;
  color: #1e293b;
}

.language {
  color: #64748b;
  font-size: 0.75rem;
}

.time {
  color: #64748b;
}

.message-content {
  word-break: break-word;
  line-height: 1.4;
}

.original-content {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 0.25rem;
  padding-left: 0.5rem;
  border-left: 2px solid #e2e8f0;
}

.translated-content {
  color: #1e293b;
}

.new-message-alert {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background: #1e293b;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:hover {
    background: #334155;
  }
}

.message-error {
  width: 100%;
  padding: 0 1rem;

  :deep(.el-alert) {
    width: 100%;
    padding-right: 30px; // 为关闭按钮预留空间
  }
}

.message-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.resend-button {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f56c6c;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:hover {
    background-color: #e6a23c;
    transform: scale(1.1);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: scale(0.95);
  }
}

.message-content-wrapper {
  flex: 1;
}
</style>
