<template>
    <!-- 广告容器，广告将会渲染在此 -->
    <ins class="adsbygoogle"
         :style="style"
         :data-ad-client="adClient"
         :data-ad-slot="adSlot"
         :data-ad-format="adFormat"></ins>
  </template>
  
  <script setup lang="ts">
  import { onMounted } from 'vue';
  
  // 定义组件 Props，方便配置广告位 ID、格式及样式
  const props = defineProps({
    adSlot: {
      type: String,
      required: true
    },
    adFormat: {
      type: String,
      default: 'auto'
    },
    style: {
      type: String,
      default: 'display:block'
    }
  });
  
  // 从环境变量获取 Google Ads 客户端 ID
  const adClient = import.meta.env.VITE_GOOGLE_ADS_CLIENT as string;
  
  onMounted(() => {
    // try {
    //   // 调用 Google Ads API 渲染广告
    //   // eslint-disable-next-line no-undef
    //   (window.adsbygoogle = window.adsbygoogle || []).push({});
    // } catch (error) {
    //   console.error('Google Ads 加载失败：', error);
    // }
  });
  </script>
  
  <style scoped>
  /* 根据需要配置广告的样式 */
  </style>