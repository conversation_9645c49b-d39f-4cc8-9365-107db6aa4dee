<template>
  <div v-if="modelValue" class="username-dialog-overlay" @click="handleOverlayClick">
    <div class="username-dialog" @click.stop>
      <h2>Set nick name</h2>
      <div class="input-group">
        <input
          v-model="inputUsername"
          type="text"
          placeholder="length must be between 2 and 20 characters"
          :class="{ error: showError }"
          @input="handleInput"
          @keyup.enter="handleConfirm"
        />
        <p v-if="showError" class="error-message"> Username length must be between 2 and 20 characters </p>
      </div>
      <div class="actions">
        <button class="cancel-button" @click="$emit('update:modelValue', false)"> Cancel </button>
        <button class="confirm-button" @click="handleConfirm" :disabled="!isValid"> OK </button>
      </div>
    </div>
  </div>
</template>

<script name="UsernameDialog" setup lang="ts">
  import { UserService } from '@/services';

  const props = defineProps<{
    modelValue: boolean;
  }>();

  const emit = defineEmits<{
    (e: 'update:modelValue', value: boolean): void;
    (e: 'confirm', username: string): void;
  }>();

  const inputUsername = ref(UserService.getUsername());
  const showError = ref(false);

  const isValid = computed(() => {
    return inputUsername.value.length >= 2 && inputUsername.value.length <= 20;
  });

  const handleInput = () => {
    showError.value = false;
  };

  const handleConfirm = () => {
    if (isValid.value) {
      emit('confirm', inputUsername.value);
      emit('update:modelValue', false);
    } else {
      showError.value = true;
    }
  };

  const handleOverlayClick = () => {
    if (UserService.getUsername()) {
      emit('update:modelValue', false);
    }
  };
</script>

<style scoped>
  .username-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .username-dialog {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  h2 {
    margin: 0 0 1.5rem;
    font-size: 1.25rem;
    color: #1e293b;
  }

  .input-group {
    margin-bottom: 1.5rem;
  }

  input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.2s;
  }

  input:focus {
    outline: none;
    border-color: #3b82f6;
  }

  input.error {
    border-color: #ef4444;
  }

  .error-message {
    margin: 0.5rem 0 0;
    color: #ef4444;
    font-size: 0.875rem;
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
  }

  button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .cancel-button {
    background-color: #e2e8f0;
    color: #475569;
  }

  .cancel-button:hover {
    background-color: #cbd5e1;
  }

  .confirm-button {
    background-color: #3b82f6;
    color: white;
  }

  .confirm-button:hover:not(:disabled) {
    background-color: #2563eb;
  }

  .confirm-button:disabled {
    background-color: #93c5fd;
    cursor: not-allowed;
  }
</style>
