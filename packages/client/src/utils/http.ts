import axios from 'axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import { cliConfig } from '@/config';
import { createCliErr, UnibabbleError, ErrorCode, type ApiResponse } from '@unibabble/shared';
import type { BackendPath } from '@/types';


const MODULE_NAME = 'Client:utils:http'

const http = axios.create();

// 请求拦截器
http.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// 响应拦截器
http.interceptors.response.use(
    (response) => {
        return response.data;
    },
    (error) => {
        if (error.response) {
            switch (error.response.status) {
                case 401:
                    // 处理未授权错误
                    break;
                case 404:
                    // 处理未找到错误
                    break;
                default:
                    // 处理其他错误
                    break;
            }
        }
        return Promise.reject(error);
    }
);

const backendApiBaseUrl = `${cliConfig.client.secureProtocol ? 'https' : 'http'}://${cliConfig.server.host}${cliConfig.server.api}`;

// 封装常用请求方法
// T: sending data type, R: response data type
export const getBackend = <T = any>(path: BackendPath, params?: any): Promise<ApiResponse<T>> => http.get(backendApiBaseUrl + path, { params });
export const postBackend = async <T = any, R = any>(
    path: BackendPath,
    data: T,
    conf?: AxiosRequestConfig<T>
): Promise<R> => {
    const rep: ApiResponse<R, T> = await http.post(backendApiBaseUrl + path, data, conf);

    // 检查 HTTP 状态码
    if (!rep) {
        // 抛出 UnibabbleError 或其他类型的错误
        throw createCliErr(
            ErrorCode.HTTP_POST_ERROR,
            'No response returned from server',
            'postBackend',
            MODULE_NAME
        );
    } else if (!rep.data) {
        throw createCliErr(
            ErrorCode.API_RESPONSE_NO_DATA,
            'API response no data',
            'postBackend',
            MODULE_NAME,
            undefined,
            undefined,
            undefined,
            { rep }
        );
    } else if (rep.data instanceof UnibabbleError) {
        throw rep.data;
    }

    return rep.data;
}

export const get = (path: string, params?: any) => http.get(path, { params });
export const post = <T = any, R = any>(path: string, data?: T, conf?: AxiosRequestConfig<T>): Promise<AxiosResponse<R>> => http.post(path, data, conf);
export const put = (path: string, data?: any) => http.put(path, data);
export const del = (path: string, params?: any) => http.delete(path, { params });

// 错误处理
export const handleError = (error: any) => {
    console.error('Request failed:', error.message);
};


export const checkRepDataError: <T>(
    rep: ApiResponse<T> | undefined,
    method?: string,
    module?: string
) => asserts rep is ApiResponse<T> & { data: NonNullable<T> } = (rep, method, module) => {
    if (!rep) {
        throw createCliErr(
            ErrorCode.API_RESPONSE_ERROR,
            'API response is undefined',
            method,
            module,
            undefined,
            undefined,
            undefined,
            { rep }
        )
    } else if (!rep.data) {
        throw createCliErr(
            ErrorCode.API_RESPONSE_NO_DATA,
            'API response no data',
            method,
            module,
            undefined,
            undefined,
            undefined,
            { ...rep }
        )
    } else if (rep.data instanceof UnibabbleError) {
        throw rep.data
    }
}
