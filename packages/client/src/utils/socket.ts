import { io } from 'socket.io-client';
import { cliConfig } from '@/config';
import type { ClientSocket } from '@unibabble/shared';
import { GoogleAuth } from '@/services';

/* 
// 1. connect() - 主动连接
socket.connect()
// 状态变化：
// - socket.connected: false -> true
// - socket.disconnected: true -> false
// - socket.io.readyState: 'closed' -> 'opening' -> 'open'

// 2. disconnect() - 完全断开连接
socket.disconnect()
// 状态变化：
// - socket.connected: true -> false
// - socket.disconnected: false -> true
// - socket.io.readyState: 'open' -> 'closed'
// - 会触发 disconnect 事件
// - 清理所有监听器
// - 需要重新 connect() 才能恢复连接

// 3. close() - 临时关闭连接
socket.close()
// 状态变化：
// - socket.connected: true -> false
// - socket.disconnected: false -> true
// - socket.io.readyState: 'open' -> 'closed'
// - 保留监听器
// - 可以通过 connect() 重新连接

// 4. 网络异常断开
// 状态变化：
// - socket.connected: true -> false
// - socket.disconnected: false -> true
// - socket.io.readyState: 'open' -> 'closed'
// - 如果启用了自动重连，会自动尝试重新连接
 */

const wsUrl = `${cliConfig.client.secureProtocol ? 'wss' : 'ws'}://${cliConfig.server.host}`

// 使用 let 声明，允许在需要时重新赋值
let socket: ClientSocket | null = null;

// 获取 socket 实例的函数
function getSocket(): ClientSocket {
    if (!socket) {
        socket = io(wsUrl, {
            auth: GoogleAuth.getToken(),
            path: cliConfig.server.ws,
            autoConnect: false,
            reconnection: true,
            reconnectionAttempts: cliConfig.connection.reconnectionAttempts,
            reconnectionDelay: cliConfig.connection.reconnectionDelay,
            reconnectionDelayMax: cliConfig.connection.reconnectionDelayMax,
            timeout: cliConfig.connection.connectTimeout,
            transports: ['websocket'],
        }) as ClientSocket;
    }

    return socket;
}

function isOpen() {
    return socket?.io._readyState === 'open'
}

export default { getSocket, isOpen }
