import { getTimestamp, logger } from '@unibabble/shared'
import { useChatStore } from '@/stores/useChatStore';

// 存储所有定时器的引用
const intervals: NodeJS.Timeout[] = [];

const MODULE_NAME = 'Client:timer'

export function showClientStateStats() {
  // 对serverState数据进行统计并显示日志
  const checkServerState = () => {
    const chatStore = useChatStore();

    logger.info('Client State Statistics', {
      module: MODULE_NAME,
      timestamp: getTimestamp(),
      details: {
        rooms: chatStore.currentRoom,
        users: chatStore.currentUser,
        Connected: chatStore.isConnected,
        isInRoom: chatStore.isInRoom,
        isListenersSetup: chatStore.isListenersSetup,
        isConnecting: chatStore.isConnecting,
        messages: Array.from(chatStore.messages.values()),
      }
    });
  }

  const interval = setInterval(checkServerState, 5000);
  intervals.push(interval);
  return interval;
}


// 清理所有定时器
export function clearAllTimers() {
  intervals.forEach(interval => clearInterval(interval));
  intervals.length = 0;
}
