import { unknown__cliErr, cliErr__errPayload } from '@unibabble/shared';
import type { ErrorPayload } from '@unibabble/shared';


const checkErrMsg = (err: unknown, desc?: string) => {
  const descStr = desc ? ` - ${desc}` : ''
  return (err instanceof Error ? desc ? err.message : desc : String(err)) + descStr
}


const isErrPayload = (err: unknown): err is ErrorPayload => {
  return (err as ErrorPayload).errMsg !== undefined
}


const catchErr = (err: unknown, method: string, moduleName: string) => {
  if (isErrPayload(err)) {
    return err
  } else {
    const clientErr = unknown__cliErr(err)
    return cliErr__errPayload(clientErr, method, moduleName)
  }
}


const doNothing = () => { }


export { catchErr, isErrPayload, checkErrMsg, doNothing };