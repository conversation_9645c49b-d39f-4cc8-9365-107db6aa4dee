/// <reference types="vite/client" />

interface ImportMetaEnv {

  readonly VITE_ENV: 'remote' | 'local'

  // log
  readonly VITE_LOG_LEVEL?: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR'

  // base
  readonly VITE_SERVER_HOST: string
  readonly VITE_SERVER_PORT?: number
  readonly VITE_WS_PATH?: string
  readonly VITE_API_PATH?: string

  // connection
  readonly VITE_RECONNECT_DELAY?: number
  readonly VITE_RECONNECT_DELAY_MAX?: number
  readonly VITE_RECONNECT_ATTEMPTS?: number
  readonly VITE_CONNECT_TIMEOUT?: number
  readonly VITE_ACK_TIMEOUT?: number

  // 应用配置
  readonly VITE_APP_TITLE?: string
  readonly VITE_APP_VERSION?: string
  readonly VITE_APP_DESCRIPTION?: string
  readonly VITE_APP_URL: `http${'s' | ''}://${string}`

  // 认证配置
  readonly VITE_AUTH_ENABLED: string
  readonly VITE_APP_GOOGLE_CLIENT_ID?: string

  // 聊天配置
  // readonly VITE_MAX_MESSAGE_LENGTH: string
  // readonly VITE_MESSAGE_RATE_LIMIT: string
  // readonly VITE_MAX_ROOM_USERS: string
  // readonly VITE_INACTIVE_TIMEOUT: string

  // client
  readonly VITE_CLIENT_HOST: string
  readonly VITE_CLIENT_PORT?: number
  readonly VITE_SECURE_PROTOCOL?: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

declare module '*.vue' {
  import type { DefineComponent } from 'vue';
  const component: DefineComponent<{}, {}, any>;
  export default component;
}


