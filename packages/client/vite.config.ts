import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { config as dotenv } from 'dotenv';
import { envFileMap } from '@unibabble/shared'
import type { EnvType } from '@unibabble/shared'

// declare namespace NodeJS {
//   interface ProcessEnv {
//     readonly VITE_ENV: 'dev' | 'remote';
//     readonly VITE_WS_PATH: string;
//     readonly VITE_SERVER_HOST: string;
//     readonly VITE_SECURE_PROTOCOL?: string;
//     readonly VITE_CLIENT_HOST?: string;
//   }
// }


const ENV = (process.env.VITE_ENV ? process.env.VITE_ENV : 'dev') as EnvType;
const envFile = envFileMap[ENV];
dotenv({ path: envFile });

// https://vite.dev/config/
export default defineConfig({
  // 基于 package.json 的 scripts 中的命令的mode参数值来读取client项目根目录下的.env.${mode} 对应配置
  mode: process.env.VITE_ENV,
  envPrefix: 'VITE_',
  plugins: [
    {
      name: 'env-config-check',
      configResolved(config) {
        console.log('=== Environment Variables ===');
        console.log('VITE_CLIENT_HOST:', process.env.VITE_CLIENT_HOST);
        console.log('VITE_SERVER_HOST:', process.env.VITE_SERVER_HOST);
        console.log('VITE_WS_PATH:', process.env.VITE_WS_PATH);
        console.log('VITE_SECURE_PROTOCOL:', process.env.VITE_SECURE_PROTOCOL);
        console.log('VITE_APP_URL:', process.env.VITE_APP_URL);
        console.log('=== Server Config ===');
        console.log('allowedHosts:', config.server.allowedHosts);
        console.log('proxy:', config.server.proxy);
      }
    },
    vue({
      script: {
        defineModel: true,
        propsDestructure: true
      }
    }),
    vueDevTools(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: [
        'vue',
        'vue-router',
        '@vueuse/core',
        'pinia'
      ],
      dts: 'src/types/auto-imports.d.ts',
      dirs: ['src/composables', 'src/stores'],
      vueTemplate: true,
      defaultExportByFilename: true,
      injectAtEnd: true
    }),
    Components({
      resolvers: [ElementPlusResolver({
        importStyle: true
      })],
      dts: 'src/types/components.d.ts',
      dirs: ['src/components'],
      directoryAsNamespace: true
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        charset: false
      }
    },
    devSourcemap: true
  },
  build: {
    sourcemap: true,
    minify: false,
    commonjsOptions: {
      include: [/@unibabble\/shared/, /node_modules/],
    },
    rollupOptions: {
      output: {
        sourcemapExcludeSources: false
      }
    }
  },
  server: {
    host: true,
    port: 5173,
    strictPort: true,
    allowedHosts: process.env.VITE_CLIENT_HOST ? [process.env.VITE_CLIENT_HOST] : [],
    proxy: {
      [process.env.VITE_WS_PATH as string]: {
        target: `${process.env.VITE_SECURE_PROTOCOL?.toLowerCase() === 'true' ? 'https' : 'http'}://${process.env.VITE_SERVER_HOST}`,
        ws: true,
      },
    },
  }
})
