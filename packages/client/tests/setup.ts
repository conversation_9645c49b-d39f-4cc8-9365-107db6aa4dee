import { config } from '@vue/test-utils';
import { beforeAll, afterEach, afterAll, vi } from 'vitest';
import { setupServer } from 'msw/node';

// 全局模拟 ResizeObserver
if (!global.ResizeObserver) {
  global.ResizeObserver = class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
}

// 设置 MSW 服务器
const server = setupServer();

// 在所有测试之前启动服务器
beforeAll(() => {
  server.listen({ onUnhandledRequest: 'error' });
  
  // 模拟 matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
});

// 重置所有请求处理程序
afterEach(() => {
  server.resetHandlers();
  vi.clearAllMocks();
});

// 关闭服务器
afterAll(() => server.close());

// 全局测试配置
config.global = {
  ...config.global,
  mocks: {},
  stubs: {},
  // 可以在这里添加全局组件、指令等
};

/////////////////////////////////////
// utils
export const testUtils = {
  async waitForTimeout(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
};
