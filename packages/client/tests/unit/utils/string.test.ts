import { describe, it, expect } from 'vitest';

/**
 * 字符串工具函数测试
 */
describe('string utils', () => {
  describe('truncate', () => {
    it('应该截断过长的字符串', () => {
      const input = '这是一段很长的文本，需要被截断';
      const result = truncate(input, 10);
      expect(result).toBe('这是一段很长的文本，...');
    });

    it('不应该截断短字符串', () => {
      const input = '短文本';
      const result = truncate(input, 10);
      expect(result).toBe(input)
    });
  });
});

// 示例工具函数
function truncate(str: string, maxLength: number): string {
  if (str.length <= maxLength) return str;
  return `${str.slice(0, maxLength)}...`;
}
