// 导入要测试的函数
import { describe, it, expect } from 'vitest';
import { isErrPayload } from '../../../src/utils';

// 描述测试套件
describe('common utils', () => {
  // 描述要测试的函数
  describe('isErrPayload', () => {
    // 测试用例1：当传入包含 errMsg 的对象时，应该返回 true
    it('当传入包含 errMsg 的对象时，应该返回 true', () => {
      // 准备测试数据
      const errorPayload = {
        errMsg: '发生了一个错误',
        errCode: 'ERROR_001'
      };

      // 执行测试
      const result = isErrPayload(errorPayload);

      // 验证结果
      expect(result).toBeTruthy()
    });

    // 测试用例2：当传入普通错误对象时，应该返回 false
    it('当传入普通错误对象时，应该返回 false', () => {
      // 准备测试数据
      const regularError = new Error('普通错误');

      // 执行测试
      const result = isErrPayload(regularError);

      // 验证结果
      expect(result).toBeFalsy()
    });

    // 测试用例3：当传入普通对象时，应该返回 false
    it('当传入普通对象时，应该返回 false', () => {
      // 准备测试数据
      const regularObject = { name: '测试' };

      // 执行测试
      const result = isErrPayload(regularObject);

      // 验证结果
      expect(result).toBeFalsy()
    });

    // 测试用例4：当传入 null 或 undefined 时，应该返回 false
    it('当传入 null 或 undefined 时，应该返回 false', () => {
      // 执行测试并验证结果
      expect(isErrPayload(null)).toBeFalsy()
      expect(isErrPayload(undefined)).toBeFalsy()
    });
  });
});
