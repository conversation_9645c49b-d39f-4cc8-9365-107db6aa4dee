// 一个简单的测试示例
import { describe, it, expect } from 'vitest';

describe('简单测试示例', () => {
  it('1 + 1 应该等于 2', () => {
    expect(1 + 1).toBe(2);
  });

  it('应该正确比较对象', () => {
    const data = { name: '测试' };
    const dataWithAge = { ...data, age: 18 };
    
    expect(dataWithAge).toEqual({
      name: '测试',
      age: 18
    });
  });
});

/*
it('重试3次', {
  retry: 3,  // 失败时重试3次
  timeout: 5000  // 5秒超时
}, async () => {
  const result = await flakyOperation();
  expect(result).toBe(true);
});

常用配置选项
  retry: 测试失败时的重试次数
  timeout: 测试超时时间（毫秒）
  skip: 是否跳过这个测试
  only: 只运行这个测试
  concurrent: 是否与其他测试并行运行
  todo: 标记为待办测试
  repeats: 测试重复次数
  shuffle: 随机运行测试
  fails: 如果测试失败，标记为通过
*/