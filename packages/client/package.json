{"name": "@unibabble/client", "version": "0.0.1", "private": true, "type": "module", "scripts": {"debug:remote": "VITE_ENV=remote vite dev --mode remote --config vite.config.ts --host --port 5173 --force", "debug:dev": "VITE_ENV=dev vite dev --mode dev --config vite.config.ts --host --port 5173 --force", "dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "test": "vitest", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:api": "vitest run tests/integration/api/", "test:unit": "vitest run tests/unit/", "test:unit:watch": "vitest watch tests/unit/", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@unibabble/shared": "workspace:*", "element-plus": "2.9.3", "lodash": "4.17.21", "pinia": "3.0.1", "vue": "^3.5.13", "vue-i18n": "^11.1.5", "vue-router": "^4.5.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1", "@testing-library/vue": "^8.1.0", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.15", "@types/node": "^20.11.0", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vitejs/plugin-vue": "^5.2.1", "@vitest/coverage-v8": "^3.2.2", "@vitest/ui": "^3.2.2", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "@vueuse/core": "^12.4.0", "eslint": "^9.28.0", "eslint-plugin-vue": "^9.28.0", "jsdom": "^24.0.0", "msw": "^2.2.1", "sass": "1.83.4", "sass-embedded": "1.83.4", "unplugin-auto-import": "19.0.0", "unplugin-vue-components": "28.0.0", "vite": "^6.1.0", "vite-plugin-vue-devtools": "^7.7.0", "vitest": "^3.2.2", "vue-tsc": "^2.2.0"}}